import Vue from 'vue'
import Router from 'vue-router'
import Layout from '@/components/common/index.vue'
// import { UserModule } from '@/store/modules/user'
import { SappModule } from '@/store/modules/app'
import { CommonModule } from '@/store/modules/common'
import Utils from '@/utils/utils'

Vue.use(Router)

export const routes: any = [
  {
    path: '/404',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/404',
        name: '404',
        component: () => import(/* webpackChunkName: "404" */ '@/views/404.vue'),
        meta: { title: '找不到该页面', icon: 'el-icon-date-solid' }
      }
    ]
  },
  {
    path: '/',
    component: Layout,
    hidden: true,
    redirect: '/task' // 重定向到 /task
  },
  {
    path: '/task',
    component: Layout,
    name: 'task',
    hidden: true,
    children: [
      {
        path: '/',
        name: 'taskIndex',
        component: () => import(/* webpackChunkName: "Index" */ '@/views/task/Index.vue'),
        meta: { title: '任务', icon: '' }
      },
      {
        path: 'create',
        name: 'taskCreate',
        component: () => import(/* webpackChunkName: "CreateTask" */ '@/views/task/create/Index.vue'),
        meta: { title: '新建任务', icon: '' }
      },
      {
        path: 'view',
        name: 'task',
        component: () => import(/* webpackChunkName: "CreateTask" */ '@/views/task/view/Index.vue'),
        meta: { title: '任务详情', icon: '' }
      }
    ]
  },
  {
    path: '/machine',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/',
        name: 'machine',
        component: () => import(/* webpackChunkName: "Index" */ '@/views/machine/Index.vue'),
        meta: { title: '机器管理', icon: '' }
      }
    ]
  },
  {
    path: '/module',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/',
        name: 'model',
        component: () => import(/* webpackChunkName: "Index" */ '@/views/module/Index.vue'),
        meta: { title: '模块管理', icon: '' }
      }
    ]
  },
  {
    path: '/module',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/',
        name: 'module',
        component: () => import(/* webpackChunkName: "Index" */ '@/views/task/Index.vue'),
        meta: { title: '任务', icon: '' }
      }
    ]
  },
  {
    path: '/topology',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/',
        name: 'topology',
        component: () => import(/* webpackChunkName: "Index" */ '@/views/topology/Index.vue'),
        meta: { title: '拓扑', icon: '' }
      },
      {
        path: '/topology/create',
        name: 'topologyCreate',
        component: () => import(/* webpackChunkName: "CreateTask" */ '@/views/topology/Detail.vue'),
        meta: { title: '新建拓扑', icon: '' }
      },
      {
        path: '/topology/detail/:id',
        name: 'topologyDetail',
        component: () => import(/* webpackChunkName: "Index" */ '@/views/topology/Detail.vue'),
        meta: { title: '拓扑详情', icon: '' }
      }
    ]
  },
  {
    path: '/htmlStyle',
    component: Layout,
    name: 'HtmlStyle',
    meta: { title: 'htmlStyle', icon: 'el-icon-save-solid', topIndex: 1 },
    children: [
      {
        path: 'index',
        name: 'HtmlStyleHome',
        component: () => import(/* webpackChunkName: "htmlStyle/index" */ '@/views/htmlStyle/index.vue'),
        meta: { title: '常用样式', topIndex: 1 }
      },
      {
        path: 'flex',
        name: 'HtmlStyleFlex',
        component: () => import(/* webpackChunkName: "htmlStyle/flex" */ '@/views/htmlStyle/Flex.vue'),
        meta: { title: 'Flex布局', topIndex: 1 }
      }
    ]
  },
  {
    path: 'routerBreadcrumb',
    name: 'RouterBreadcrumb',
    component: () => import(/* webpackChunkName: "tree" */ '@/views/routerBreadcrumb/index.vue'),
    meta: { title: '路由面包屑', icon: 'el-icon-chat-solid', level: 2 },
    children: [
      {
        path: 'page1',
        name: 'Page1',
        component: () => import(/* webpackChunkName: "page1" */ '@/views/routerBreadcrumb/children/page1/index.vue'),
        meta: { title: 'Page1', icon: 'el-icon-comment-solid', level: 3 },
        children: [
          {
            path: 'page11',
            name: 'Page11',
            component: () => import(/* webpackChunkName: "page11" */ '@/views/routerBreadcrumb/children/page1/children/page1-1/index.vue'),
            meta: { title: 'Page11', icon: 'el-icon-comment-solid', level: 4 }
          },
          {
            path: 'page12',
            name: 'Page12',
            component: () => import(/* webpackChunkName: "page11" */ '@/views/routerBreadcrumb/children/page1/children/page1-2/index.vue'),
            meta: { title: 'Page12', icon: 'el-icon-comment-solid', level: 4 }
          }
        ]
      },
      {
        path: 'page2',
        name: 'Page2',
        component: () => import(/* webpackChunkName: "page2" */ '@/views/routerBreadcrumb/children/page2/index.vue'),
        meta: { title: 'Page2', icon: 'el-icon-comment-solid', level: 3 }
      },
      {
        path: 'page3',
        name: 'Page3',
        component: () => import(/* webpackChunkName: "page3" */ '@/views/routerBreadcrumb/children/page3/index.vue'),
        meta: { title: 'Page3', icon: 'el-icon-comment-solid' }
      }
    ]
  },
  {
    path: '/',
    component: Layout,
    children: [
      {
        path: '/tsForm',
        component: () => import(/* webpackChunkName: "Form" */ '@/views/tsForm/index.vue'),
        name: 'TsForm',
        meta: { title: '查询列表页(标准)' }
      },
      {
        path: '/tsForm/tsForm2',
        component: () => import(/* webpackChunkName: "Form" */ '@/views/tsForm/index.vue'),
        name: 'TsForm2',
        meta: { title: '查询列表页(标准)', notInMenu: true }
      },
      {
        path: '/form',
        component: () => import(/* webpackChunkName: "Form" */ '@/views/form/Index.vue'),
        name: 'Form',
        meta: { title: '查询列表页(标准)' }
      },
      {
        path: '/formTypical',
        component: () => import(/* webpackChunkName: "Form" */ '@/views/form/Typical.vue'),
        name: 'Form2',
        meta: { title: '查询列表页(滚动)' }
      },
      {
        path: '/formAdvanced',
        component: () => import(/* webpackChunkName: "Form" */ '@/views/form/FormAdvanced.vue'),
        name: 'Form3',
        meta: { title: '查询列表页(高级)' }
      },
      {
        path: '/formAdvanced2',
        component: () => import(/* webpackChunkName: "Form" */ '@/views/form/FormAdvanced2.vue'),
        name: 'Form3-1',
        meta: { title: '查询列表页(高级2)' }
      },
      {
        path: '/formCard',
        component: () => import(/* webpackChunkName: "Form" */ '@/views/form/FormCard.vue'),
        name: 'Form4',
        meta: { title: '查询列表页(卡片式)' }
      },
      {
        path: '/formImg',
        component: () => import(/* webpackChunkName: "Form" */ '@/views/form/FormImg.vue'),
        name: 'Form5',
        meta: { title: '查询列表页(带图片)' }
      },
      {
        path: '/formDataJump',
        component: () => import(/* webpackChunkName: "Form" */ '@/views/form/FormDataJump.vue'),
        name: 'Form6',
        meta: { title: '查询列表页(数据跳转)' }
      },
      {
        path: '/formDataToggle',
        component: () => import(/* webpackChunkName: "Form" */ '@/views/form/FormDataToggle.vue'),
        name: 'Form7',
        meta: { title: '查询列表页(数据切换)' }
      },
      {
        path: '/detail',
        name: 'Detail',
        component: () => import(/* webpackChunkName: "detail" */ '@/views/detail/Index.vue'),
        meta: { title: '详情页(标准/高级)' }
      },
      {
        path: '/detailMultilevel',
        name: 'DetailMultilevel',
        component: () => import(/* webpackChunkName: "detail" */ '@/views/detail/DetailMultilevel.vue'),
        meta: { title: '详情页(多级)' }
      },
      {
        path: '/drawerDetail',
        name: 'drawerDetail',
        component: () => import(/* webpackChunkName: "drawerDetail" */ '@/views/drawerDetail/Index.vue'),
        meta: { title: '抽屉-侧边展开详情' }
      },
      {
        path: '/create',
        name: 'create',
        component: () => import(/* webpackChunkName: "create" */ '@/views/create/Index.vue'),
        meta: { title: '新建' }
      },
      {
        path: '/notice',
        name: 'notice',
        component: () => import(/* webpackChunkName: "notice" */ '@/views/notice/Index.vue'),
        meta: { title: '公告列表' }
      },
      {
        path: '/notice/detail',
        name: 'noticeDetail',
        component: () => import(/* webpackChunkName: "noticeDetail" */ '@/views/notice/Detail.vue'),
        meta: { title: '公告详情' }
      },
      {
        path: '/kanban',
        name: 'kanban',
        component: () => import(/* webpackChunkName: "noticeDetail" */ '@/views/kanban/Index.vue'),
        meta: { title: '数据看板' }
      }
    ]
  },
  {
    path: '/nested',
    component: Layout,
    redirect: '/nested/menu1',
    name: 'Nested',
    children: [
      {
        path: 'menu1',
        component: () => import(/* webpackChunkName: "menu1" */ '@/views/nested/menu1/index.vue'),
        name: 'Menu1',
        meta: { title: 'Menu1' },
        children: [
          {
            path: 'menu1-1',
            component: () => import(/* webpackChunkName: "menu1-1" */ '@/views/nested/menu1/menu1-1/index.vue'),
            name: 'Menu1-1',
            meta: { title: 'Menu1-1' }
          },
          {
            path: 'menu1-2',
            component: () => import(/* webpackChunkName: "menu1-2" */ '@/views/nested/menu1/menu1-2/index.vue'),
            name: 'Menu1-2',
            meta: { title: 'Menu1-2' },
            children: [
              {
                path: 'menu1-2-1',
                component: () => import(/* webpackChunkName: "menu1-2-1" */ '@/views/nested/menu1/menu1-2/menu1-2-1/index.vue'),
                name: 'Menu1-2-1',
                meta: { title: 'Menu1-2-1' }
              },
              {
                path: 'menu1-2-2',
                component: () => import(/* webpackChunkName: "menu1-2-2" */ '@/views/nested/menu1/menu1-2/menu1-2-2/index.vue'),
                name: 'Menu1-2-2',
                meta: { title: 'Menu1-2-2' }
              }
            ]
          },
          {
            path: 'menu1-3',
            component: () => import(/* webpackChunkName: "menu1-3" */ '@/views/nested/menu1/menu1-3/index.vue'),
            name: 'Menu1-3',
            meta: { title: 'Menu1-3' }
          }
        ]
      },
      {
        path: 'menu2',
        component: () => import(/* webpackChunkName: "menu2" */ '@/views/nested/menu2/index.vue'),
        meta: { title: 'menu2' }
      }
    ]
  },
  {
    path: '/dialog',
    component: Layout,
    children: [
      {
        path: '/dialog',
        component: () => import(/* webpackChunkName: "Form" */ '@/views/dialog/Index.vue'),
        name: 'Dialog',
        meta: { title: '弹窗' }
      }
    ]
  },
  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/404', hidden: true }
]

// 找不到页面的路由，需要追加在路由表末尾
const notFoundRoutes = [
  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/404', hidden: true }
]

const router: any = new Router({
  mode: 'history', // 推荐使用 history 便于埋点统计且地址栏路径较直观
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { x: 0, y: 0 }
    }
  }
})

// 全局路由状态
let checkStatus: boolean = false

// 前端校验路由权限 [通过接口返回的路由与本地全量的路由进行对比]
const checkRouterRole = (routers: any, checkRouter: string) => {
  routers.forEach((route: any) => {
    if (route.name === checkRouter) {
      checkStatus = true
      return checkStatus
    }
    return (route.children && route.children.length && checkRouterRole(route.children, checkRouter))
  })
  return checkStatus
}

// 依赖服务端接口动态注册路由
// const dynamicRegistRouters = (routers: any) => {
//   router.addRoutes()
// }

// 递归替换异步路由为对应组件
const replaceAsyncRoutes: any = (routes: any, asyncRoutes: any) => {
  routes.forEach((item: any, index: number) => {
    const { name, path, meta, href, type } = item
    const curRoute: any = {
      name,
      path,
      meta,
      children: []
    }
    asyncRoutes.push(curRoute)
    if (item.children && item.children.length) {
      replaceAsyncRoutes(item.children, asyncRoutes[index].children)
    }
  })
}

// 添加服务端异步路由
export const addAsyncRoutes: any = async(routes: any) => {
  const asyncRoutes: any[] = []
  routes.forEach((item: any, index: number) => {
    if (item && item.children) {
      asyncRoutes[index] = {
        path: '/',
        component: Layout,
        children: []
      }
      replaceAsyncRoutes(item.children, asyncRoutes[index].children)
    }
  })
  let allRoutes: any = []
  allRoutes = allRoutes.concat(asyncRoutes, notFoundRoutes)
  await router.addRoutes(allRoutes)
  // 兼容 vue-router this.$router.options.routes 不赋值的问题
  router.options.routes = router.options.routes.concat(allRoutes)
}

async function checkAndFetchData(route:any) {
  if (route.path === '/' || (CommonModule.userId === 0 && CommonModule.userList.length === 0)) {
    await CommonModule.checkLoginAndRedirect()
    await CommonModule.getAllUser()
  }
}

// 路由守卫
router.beforeEach(async(to: any, from: any, next: any) => {
  SappModule.INIT_FRAMEWORK()
  // 使用keepAlive缓存组件
  SappModule.KEEP_ALIVE(to.name)
  // 重置路由状态
  checkStatus = false
  // 校验登录
  console.log('校验登录')

  await checkAndFetchData(to)
  // const loginStatus = await UserModule.checkLogin()
  // 如无登录态则终止流程
  // 获取用户信息
  // await UserModule.getUserInfo()
  // 获取菜单
  if (!SappModule.menus.length) {
    await SappModule.getMenus()
    // 追加iframe路由
    // await addAsyncRoutes(SappModule.menus)
  }
  // 校验菜单权限
  next()
  // const routerWhiteList = ['/', '404']
  // if (routerWhiteList.some(white => white === to.name) || checkRouterRole(SappModule.menus, to.name)) {
  //   next()
  // } else {
  //   next({ name: '404' })
  // }
})

export default router

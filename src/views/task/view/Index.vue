<template>
  <div class="jdl-form-view">
    <!-- 动态面包屑 -->
    <RouterBreadcrumb />
    <div class="task-detail">
      <task-operation-card :task-info="taskInfo" :task-id="taskId" />
      <el-tabs v-model="activeTab" class="tab-container">
        <el-tab-pane label="拓扑图" name="topology">
          <TopologyGraph
            :task-info="taskInfo"
            :active-tab="activeTab"
            @item-click="handleItemClick"
            @node-click="handleNodeClick"
            @do-node-action="handleNodeAction"
          />
        </el-tab-pane>
        <el-tab-pane label="节点信息" name="nodeInfo">
          <NodeInfo :task-info="taskInfo" />
        </el-tab-pane>
        <el-tab-pane label="节点配置文件" name="nodeConfig">
          <NodeConfig :task-info="taskInfo" />
        </el-tab-pane>
        <el-tab-pane label="节点diff" name="nodeDiff">
          <NodeDiff :active-tab="activeTab" />
        </el-tab-pane>
        <el-tab-pane label="build日志" name="buildLog">
          <BuildLog :active-tab="activeTab" :node-id="currentNode.id" :task-id="taskId" />
        </el-tab-pane>
        <el-tab-pane label="进程信息" name="processInfo">
          <process-info :task-id="taskId" :node-id="currentNode.id" :active-tab="activeTab" />
        </el-tab-pane>
        <el-tab-pane label="指标数据" name="metrics">
          <metrics-data :task-id="taskId" :active-tab="activeTab" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import API from '@/api'
import TopologyGraph from './components/TopologyGraph.vue'
import NodeInfo from './NodeInfo.vue'
import NodeConfig from './NodeConfig.vue'
import NodeDiff from './components/NodeDiff.vue'
import BuildLog from './components/BuildLog.vue'
import ProcessInfo from './ProcessInfo.vue'
import MetricsData from './components/MetricsData.vue'
import RouterBreadcrumb from '@/components/RouterBreadcrumb/index.vue'
import { TaskModule } from '@/store/modules/task'
import TaskOperationCard from '@/views/task/view/components/TaskOperationCard.vue'
import * as _ from 'lodash'
import { ACTION_TYPES } from '@/settings/task'
export default {
  components: {
    TopologyGraph,
    NodeInfo,
    NodeConfig,
    NodeDiff,
    BuildLog,
    ProcessInfo,
    MetricsData,
    RouterBreadcrumb,
    TaskOperationCard
  },
  data() {
    return {
      taskId: 0, // 存储从 URL 中获取的 taskId
      currentNode: {
        id: 0
      },
      taskInfo: {}, // 存储当前任务的信息
      activeTab: 'topology',
      authDialogVisible: false,
      newUser: '',
      reportVisible: false
    }
  },
  computed: {
    taskNodeInfo() {
      return TaskModule.taskNodeInfo
    }
  },
  async mounted() {
    // 组件初始化时执行
    if (this.initTaskId()) {
      await this.fetchTaskInfo()
      await this.fetchTaskNodeList()
      // this.$refs.child.fetchTopologyInfo()
    }
  },
  methods: {
    // 初始化方法：从 URL 中获取 taskId
    initTaskId() {
      this.taskId = this.$route.query.taskId
      if (!this.taskId) {
        console.warn('未找到 taskId 参数')
        this.$message.warning('任务 ID 缺失，请检查 URL')
        return false
      }
      return true
    },
    async fetchTaskNodeList() {
      console.log('fetch 任务节点信息:', this.taskId)
      await TaskModule.fetchTaskNodeList(this.taskId)
      if (!this.taskId) return
      // 模拟异步请求
      console.log('任务节点信息:', this.taskId)
      console.log('任务节点信息:', this.taskNodeInfo)
      this.currentNode = this.taskNodeInfo
    },
    // 根据 taskId 获取任务信息
    async fetchTaskInfo() {
      if (!this.taskId) return
      try {
        // 模拟异步请求
        const response = await TaskModule.fetchTaskInfo(this.taskId)
        this.taskInfo = response
        console.log('任务信息:', this.taskInfo)
      } catch (error) {
        console.error('获取任务信息失败:', error)
        this.$message.error('获取任务信息失败，请稍后重试')
      }
    },
    // 处理拓扑子组件传递的数据
    handleItemClick(item) {
      console.log('父组件接收到子组件传递的数据:', item)
      // 在这里执行父组件的逻辑
    },
    async handleNodeClick(item) {
      this.currentNode = item
      await TaskModule.fetchTaskNodeInfo({ taskId: this.taskId, nodeId: item.id })
    },
    async handleNodeAction(action) {
      if (action.type === ACTION_TYPES.TAB) {
        this.activeTab = action.name
        return
      }
      console.log('unknown operation type: ', action)
    }
  }
}
</script>

<style scoped>

.tab-container {
  margin-top: 20px;
}
.el-descriptions-item__cell {
  width: 120px
}
</style>

<template>
  <div v-if="isVisible" class="node-diff-container">
    <div class="diff-toolbar">
      <div class="node-selection">
        <div class="node-select-item">
          <span class="label">参照节点：</span>
          <el-select v-model="baseNodeId" placeholder="选择参照节点" disabled>
            <el-option
              v-for="node in nodeList"
              :key="node.id"
              :label="node.name"
              :value="node.id"
              :disabled="node.id === compareNodeId"
            />
          </el-select>
        </div>
        <div class="node-select-item">
          <span class="label">对比节点：</span>
          <el-select v-model="compareNodeId" placeholder="选择对比节点">
            <el-option
              v-for="node in nodeList"
              :key="node.id"
              :label="node.name"
              :value="node.id"
              :disabled="node.id === baseNodeId"
            />
          </el-select>
        </div>
        <el-button type="primary" :disabled="!canCompare" @click="handleCompare">对比</el-button>
      </div>
    </div>

    <div v-if="diffData" class="diff-content">
      <el-row :gutter="20" class="horizontal-layout">
        <!-- 左侧文件树 -->
        <el-col :span="6" class="file-tree-container">
          <div class="file-tree-header">文件列表</div>
          <el-tree
            :data="diffFileList"
            :props="defaultProps"
            default-expand-all
            @node-click="handleFileClick"
          >
            <template #default="{ node }">
              <span>
                <i class="el-icon-document" />
                {{ node.label }}
              </span>
            </template>
          </el-tree>
        </el-col>
        <!-- 右侧文档内容 -->
        <el-col :span="18" class="editor-wrapper">
          <div v-if="selectedFile" class="diff2html-container">
            <div class="diff2html-content" v-html="diffHtml" />
          </div>
          <div v-else class="placeholder">
            请选择要查看的文件
          </div>
        </el-col>
      </el-row>
    </div>
    <div v-else-if="isCompared" class="no-diff-data">
      <el-empty description="没有差异数据" />
    </div>
    <div v-else class="placeholder">
      请选择要对比的节点并点击对比按钮
    </div>
  </div>
</template>

<script>
import API from '@/api'
import { TaskModule } from '@/store/modules/task'
import { TASK_NODE_LIST } from '@/settings/taskNode'
import { parse, html } from 'diff2html'
import 'diff2html/bundles/css/diff2html.min.css'

export default {
  name: 'NodeDiff',
  props: {
    activeTab: {
      type: String,
      default: '',
      required: true
    }
  },
  data() {
    return {
      tabName: 'nodeDiff',
      baseNodeId: null,
      compareNodeId: null,
      diffData: null,
      diffMap: {},
      diffFileList: [],
      selectedFile: null,
      selectedFileContent: '',
      diffHtml: '',
      isCompared: false,
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      diff2htmlConfig: {
        drawFileList: false,
        matching: 'lines',
        outputFormat: 'side-by-side',
        renderNothingWhenEmpty: false,
        colorScheme: 'light',
        diffStyle: 'word',
        showFiles: true
      }
    }
  },
  computed: {
    taskNodeInfo() {
      return TaskModule.taskNodeInfo || {}
    },
    taskNodeList() {
      return TaskModule.taskNodeList || []
    },
    nodeList() {
      return this.taskNodeList.filter(node => node.id !== this.taskNodeInfo.id)
        .concat([this.taskNodeInfo])
    },
    canCompare() {
      return this.baseNodeId && this.compareNodeId && this.baseNodeId !== this.compareNodeId
    },
    isVisible() {
      // 只有当节点类型为普通模块(type!=2)时才显示
      return this.taskNodeInfo && this.taskNodeInfo.type !== TASK_NODE_LIST.TOOL
    }
  },
  watch: {
    activeTab: {
      handler(newVal) {
        if (newVal === this.tabName && this.taskNodeInfo) {
          this.baseNodeId = this.taskNodeInfo.id
        }
      },
      immediate: true
    },
    taskNodeInfo: {
      handler(newVal) {
        if (newVal && newVal.id) {
          this.baseNodeId = newVal.id
          // 重置对比状态
          this.resetCompareState()
        }
      },
      immediate: true
    }
  },
  mounted() {
  },
  methods: {
    resetCompareState() {
      this.compareNodeId = null
      this.diffData = null
      this.diffMap = {}
      this.diffFileList = []
      this.selectedFile = null
      this.selectedFileContent = ''
      this.diffHtml = ''
      this.isCompared = false
    },
    async handleCompare() {
      if (!this.canCompare) return

      try {
        const response = await API.configDiff({
          taskId: this.taskNodeInfo.taskId,
          nodeId: this.baseNodeId,
          compareNodeId: this.compareNodeId
        })

        this.isCompared = true

        if (response.data) {
          this.diffData = response.data
          this.processDiffFiles(this.diffData)
        } else {
          this.diffData = null
          this.diffMap = {}
          this.diffFileList = []
          this.selectedFile = null
          this.selectedFileContent = ''
          this.diffHtml = ''
        }
      } catch (error) {
        console.error('获取配置差异失败:', error)
        this.$message.error('获取配置差异失败，请稍后重试')
      }
    },
    processDiffFiles(fileData) {
      const arr = fileData.split('\n')
      let fileName
      const diffFileArr = []
      this.diffMap = {}

      for (let i = 0; i < arr.length; i++) {
        const str = arr[i]
        if (str.indexOf('diff --git') === 0) { // 遇到一个diff文件
          // 从diff --git a/path/to/file.txt b/path/to/file.txt 中提取文件名
          const gitDiffParts = str.match(/diff --git a\/(.+) b\/(.+)/)
          if (gitDiffParts && gitDiffParts.length >= 3) {
            // 使用正则表达式提取文件名
            fileName = gitDiffParts[1].split('/').pop()
            // 保存完整的diff内容，包括头部信息
            this.diffMap[fileName] = [str]
            diffFileArr.push({ name: fileName, fullName: gitDiffParts[1] })
          } else {
            // 回退到原来的方法
            const temp = str.split('/')
            fileName = temp[temp.length - 1]
            const fullName = str.substr(11)
            this.diffMap[fileName] = [str]
            diffFileArr.push({ name: fileName, fullName: fullName })
          }
        } else if (fileName) {
          this.diffMap[fileName].push(str)
        }
      }
      this.diffFileList = diffFileArr
      // 如果有文件，默认选择第一个
      if (diffFileArr.length > 0) {
        this.handleFileClick(diffFileArr[0])
      } else {
        // 如果没有找到文件，但有diff内容，直接渲染
        this.selectedFile = { name: 'diff' }
        this.renderDiff2Html(fileData)
      }
    },
    handleFileClick(file) {
      this.selectedFile = file
      if (file && this.diffMap[file.name]) {
        this.selectedFileContent = this.diffMap[file.name].join('\n')
        this.renderDiff2Html(this.selectedFileContent)
      } else {
        this.selectedFileContent = ''
        this.diffHtml = ''
      }
    },
    renderDiff2Html(diffText) {
      try {
        // 使用diff2html将diff文本转换为HTML
        const diffJson = parse(diffText)
        this.diffHtml = html(diffJson, this.diff2htmlConfig)
      } catch (error) {
        this.diffHtml = '<div class="error-message">渲染差异内容失败</div>'
      }
    }
  }
}
</script>

<style scoped>
.node-diff-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.diff-toolbar {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.node-selection {
  display: flex;
  align-items: center;
}

.node-select-item {
  margin-right: 20px;
  display: flex;
  align-items: center;
}

.node-select-item .label {
  margin-right: 10px;
  font-weight: bold;
  width: 120px
}

.diff-content {
  flex: 1;
  overflow: hidden;
}

.horizontal-layout {
  height: calc(100vh - 250px);
}

.file-tree-container {
  height: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow-y: auto;
}

.file-tree-header {
  padding: 10px;
  font-weight: bold;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
}

.editor-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.diff2html-container {
  flex: 1;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: auto;
  background-color: #fff;
  padding: 10px;
}

.diff2html-content {
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
}

.placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #909399;
  font-size: 16px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
}

.no-diff-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.error-message {
  color: #f56c6c;
  padding: 20px;
  text-align: center;
}
</style>

<style>
/* 全局样式，确保能应用到diff2html */
.d2h-wrapper {
  margin: 0 !important;
}

.d2h-file-header {
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
  padding: 10px;
}

.d2h-file-name {
  font-weight: bold;
}

.d2h-code-line {
  padding: 0 8px;
}

.d2h-code-line-ctn {
  white-space: pre-wrap;
}

.d2h-code-linenumber {
  color: #909399;
}

.d2h-code-side-linenumber {
  color: #909399;
}

.d2h-code-line-prefix {
  display: inline-block;
  width: 10px;
}

.d2h-addition {
  background-color: rgba(155, 255, 155, 0.2);
}

.d2h-deletion {
  background-color: rgba(255, 155, 155, 0.2);
}
</style>

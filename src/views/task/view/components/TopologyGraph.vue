<template>
  <div>
    <div class="img-container" />
    <div ref="myPage" style="height:600px;">
      <RelationGraph
        ref="graphRef"
        :options="graphOptions"
        :on-node-click="onNodeClick"
        :on-canvas-click="onCanvasClick"
      />
    </div>
    <div
      v-show="isShowNodeMenuPanel"
      class="node-menu-panel"
      :style="{left: nodeMenuPanelPosition.x + 'px', top: nodeMenuPanelPosition.y + 'px' }"
    >
      <div class="c-node-tag">请选择操作：</div>
      <div v-for="action in tabActionList"
           :key="action.name"
           class="c-node-menu-item"
           @click.stop="doAction(action)"
      >
        {{ action.label }}
      </div>
      <div v-for="action in operationActionList"
           :key="action.name"
           class="c-node-menu-item"
           @click.stop="doNodeAction(action)"
      >
        {{ action.label }}
      </div>
    </div>
  </div>
</template>

<script>
import RelationGraph from 'relation-graph'
import API from '@/api'
import { TASK_STATES_ENUM } from '@/settings/constant'
import { TASK_NODE_ALLOCATE_LIST, TASK_NODE_LIST } from '@/settings/taskNode'
import { MDC_METRIC_LIST, MDC_METRIC_TRANSFORM, MDC_QUERY_LIST, ACTION_TYPES } from '@/settings/task'
export default {
  name: 'TopologyGraph',
  components: {
    RelationGraph
  },
  props: {
    taskInfo: {
      type: Object,
      required: false
    },
    activeTab: {
      type: String,
      default: '',
      required: true
    }
  },
  data() {
    return {
      tabName: 'topology',
      isShowNodeMenuPanel: false,
      currentNode: null,
      nodeMenuPanelPosition: {
        x: 0,
        y: 0
      },
      tabActionList: [
        {
          label: '节点信息',
          name: 'nodeInfo',
          type: ACTION_TYPES.TAB
        }, {
          label: '节点文件',
          name: 'nodeConfig',
          type: ACTION_TYPES.TAB
        }, {
          label: '节点diff',
          name: 'nodeDiff',
          type: ACTION_TYPES.TAB
        }, {
          label: 'build日志',
          name: 'buildLog',
          type: ACTION_TYPES.TAB
        }, {
          label: '进程信息',
          name: 'processInfo',
          type: ACTION_TYPES.TAB
        }
      ],
      operationActionList: [],
      graphOptions: {
        defaultNodeBorderWidth: 0,
        defaultLineShape: 3,
        defaultNodeColor: 'rgba(238, 178, 94, 1)',
        defaultLineWidth: 1,
        // placeSingleNode: true,
        // 禁用数据缩放
        disableZoom: true,
        // 禁用线条
        allowSwitchLineShape: false,
        allowSwitchJunctionPoint: false,
        defaultJunctionPoint: 'border',
        zoomToFitWhenRefresh: true,
        // 设置初始缩放比例
        initialZoom: 1,
        // 设置初始位置
        initialPosition: {
          x: 0,
          y: 0
        },
        layout: {
          label: '中心',
          layoutName: 'tree',
          centerOffset_x: 0,
          centerOffset_y: 0,
          distance_coefficient: 1,
          from: 'top',
          levelDistance: '',
          min_per_width: 100,
          max_per_width: 500,
          min_per_height: 300,
          max_per_height: 500,
          maxLayoutTimes: 300,
          force_node_repulsion: 1,
          force_line_elastic: 1,
          layoutDirection: 'v'
        }
      },
      // 当前拓扑详情
      dataFrom: {
        name: null,
        describeInfo: null,
        isAutoPlacement: false,
        helpUrl: null,
        attr: null, // 属性
        notOversell: false,
        erp: null,
        attrList: [],
        nodeLines: [],
        nodes: [],
        // 有权限的用户
        powerUserList: []
      },
      currentTask: this.taskInfo,
      intervalId: null
    }
  },
  watch: {
    async taskInfo(newName, oldName) {
      console.log('taskInfo', newName)
      if (newName) {
        this.currentTask = newName
        await this.init()
      }
    },
    activeTab(newVal, oldVal) {
      console.log('activeTab', newVal)
      if (newVal) {
        if (newVal === this.tabName) {
          if (this.intervalId) {
            clearInterval(this.intervalId)
          }
          this.intervalId = setInterval(this.fetchMdcInfo, 1000)
        } else {
          if (this.intervalId) {
            clearInterval(this.intervalId)
            this.intervalId = null
          }
        }
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      // 刷新，重新设置布局
      this.$refs.graphRef.getInstance().refresh()
    })
  },
  created() {
    this.intervalId = setInterval(this.fetchMdcInfo, 1000)
  },
  beforeDestroy() {
    clearInterval(this.intervalId)
  },
  methods: {
    onCanvasClick(event) {
      this.isShowNodeMenuPanel = false
    },
    async init() {
      await this.fetchTopologyInfo()
      this.showSeeksGraph()
    },
    async fetchMdcInfo() {
      console.log('fetchMdcpInfo')
      const now = Date.now()
      const queryForm = {
        queryType: MDC_QUERY_LIST.SECOND,
        // 该指标在不同粒度下命名有所不同，需要转换
        metrics: [MDC_METRIC_TRANSFORM[MDC_QUERY_LIST.SECOND][MDC_METRIC_LIST.CPU_USAGE]],
        timeRange: [now + 1000, now + 1000],
        taskId: this.currentTask.id
      }
      const res = await API.queryMdcRange(queryForm)
    },
    handleItemClick(item, event) {
      // 触发父组件的方法，并传递点击的 item 数据
      this.$emit('item-click', item)
    },
    onNodeClick(node, event) {
      this.isShowNodeMenuPanel = true
      this.calculateActionListPos(node, event)
      this.operationActionList = this.getNodeActionOperations(node)
      this.$emit('node-click', node)
    },
    calculateActionListPos(node, event) {
      this.currentNode = node
      const basePosition = this.$refs.myPage.getBoundingClientRect()

      this.nodeMenuPanelPosition.x = event.clientX - basePosition.x
      this.nodeMenuPanelPosition.y = event.clientY - basePosition.y

      this.$nextTick(() => {
        // Get the menu element using the class selector
        const menuElement = document.querySelector('.node-menu-panel')
        if (menuElement) {
          const menuRect = menuElement.getBoundingClientRect()

          const containerWidth = basePosition.width
          if (this.nodeMenuPanelPosition.x + menuRect.width > containerWidth) {
            this.nodeMenuPanelPosition.x = containerWidth - menuRect.width - 10 // 10px padding
          }

          const containerHeight = basePosition.height
          if (this.nodeMenuPanelPosition.y + menuRect.height > containerHeight) {
            this.nodeMenuPanelPosition.y = containerHeight - menuRect.height - 10 // 10px padding
          }
        }
      })
    },
    async fetchTopologyInfo() {
      if (!this.currentTask) {
        return
      }
      // 没有拓扑的情况，默认查节点列表第一个节点
      if (!this.currentTask.topologyId) {
        const response = await API.fetchTaskNodeList({ taskId: this.currentTask.id })
        console.log(response.data)
        if (response.data) {
          this.dataFrom.nodes = [
            {
              id: response.data[0].nodeId,
              text: response.data[0].name,
              name: response.data[0].name,
              type: response.data[0].type
            }
          ]
        }
        return
      }
      // 调用 API 获取任务列表
      const response = await API.fetchTaskTopolopyDetailById({
        id: this.currentTask.topologyId,
        taskId: this.currentTask.id
      })
      if (response.data) {
        this.dataFrom = response.data
      }
    },
    showSeeksGraph() {
      if (!this.dataFrom.nodes) {
        return
      }
      const params = {
        rootId: this.currentTask.topologyId,
        nodes: this.dataFrom.nodes,
        lines: this.dataFrom.nodeLines
      }
      const graphJsonData = params
      if (!graphJsonData) {
        console.log('数据非法')
        return
      }
      const graphInstance = this.$refs.graphRef.getInstance()
      graphInstance.setJsonData(graphJsonData, () => {
        // Called when the relation-graph is completed
      })
      // graphInstance.refresh()
    },
    doAction(action) {
      this.isShowNodeMenuPanel = false
      this.$emit('do-node-action', action)
    },
    async doNodeAction(action) {
      if (action.func) {
        const [methodName, taskId, nodeId] = action.name.split('_')
        await action.func(taskId, nodeId)
      }
      this.$emit('do-node-action', action)
    },
    // todo: 后续跟进处理权限部分
    getNodeActionOperations(node) {
      console.log('getNodeActionOperations', node)
      // 根据当前节点的信息，设置菜单的操作内容
      if (!node || !this.currentTask || !this.dataFrom.nodes) {
        this.operationActionList = []
        return
      }
      // 从relation-graph获取节点ID
      const nodeId = node.id
      const taskId = this.currentTask.id
      // 从dataFrom.nodes中查找完整的节点数据
      const action = []
      if (this.checkBuild()) {
        action.push({
          label: '重搭建',
          name: 'redeployNode_' + taskId + '_' + nodeId,
          type: ACTION_TYPES.NODE_OPERATION,
          func: this.handleBuild
        })
      }
      if (this.checkReplaceNode()) {
        action.push({
          label: '更换节点资源',
          name: 'replaceNode_' + taskId + '_' + nodeId,
          type: ACTION_TYPES.NODE_OPERATION,
          func: this.handleReplaceNode
        })
      }
      if (this.checkDictExpPath()) {
        action.push({
          label: '实验词表重置',
          name: 'expDictReset_' + taskId + '_' + nodeId,
          type: ACTION_TYPES.NODE_OPERATION,
          func: this.handleDictExpPath
        })
      }
      if (this.checkUpdateScript()) {
        action.push({
          label: '更新配置',
          name: 'updateConfig_' + taskId + '_' + nodeId,
          type: ACTION_TYPES.NODE_OPERATION,
          func: this.handleUpdateScript
        })
      }
      if (this.checkCodeRenew()) {
        action.push({
          label: '代码更新',
          name: 'renewNodeCode_' + taskId + '_' + nodeId,
          type: ACTION_TYPES.NODE_OPERATION,
          func: this.handleCodeRenew
        })
      }
      return action
    },
    checkReplaceNode() {
      const status = this.taskInfo.status
      if (status !== TASK_STATES_ENUM.BUILT.value &&
        status !== TASK_STATES_ENUM.RAN.value &&
        status !== TASK_STATES_ENUM.RUN_FAIL.value &&
        status !== TASK_STATES_ENUM.BUILD_FAIL.value) {
        return false
      }
      return !(this.currentNode.machineId === null ||
        this.currentNode.allocateType === TASK_NODE_ALLOCATE_LIST.ALLOCATE_DYNAMIC)
    },
    async handleReplaceNode(taskId, nodeId) {
      console.log('handleReplaceNode: ', taskId, nodeId)
      await API.replaceNode({ taskId, nodeId })
      this.$message.success('节点更换成功')
    },
    checkDictExpPath() {
      return !!this.currentNode.taskExpDictPath
    },
    async handleDictExpPath(taskId, nodeId) {
      console.log('dict exp path')
    },
    checkUpdateScript() {
      const type = this.currentNode.type
      if (type === TASK_NODE_LIST.TOOL) {
        return false
      }
      if (!this.currentNode.updateScript) {
        return false
      }
      const status = this.taskInfo.status
      return status === TASK_STATES_ENUM.BUILT.value ||
        status === TASK_STATES_ENUM.RE_DOWNLOADED.value ||
        status === TASK_STATES_ENUM.RE_DOWNLOAD_FAIL.value ||
        status === TASK_STATES_ENUM.RAN.value ||
        status === TASK_STATES_ENUM.BUILD_FAIL.value
    },
    async handleUpdateScript(taskId, nodeId) {
      console.log('update script', taskId, nodeId)
    },
    checkCodeRenew() {
      const status = this.taskInfo.status
      if (status === TASK_STATES_ENUM.BUILT.value ||
        status === TASK_STATES_ENUM.RE_DOWNLOADED.value ||
        status === TASK_STATES_ENUM.RE_DOWNLOAD_FAIL.value ||
        status === TASK_STATES_ENUM.RAN.value ||
        status === TASK_STATES_ENUM.BUILD_FAIL.value ||
        status === TASK_STATES_ENUM.RUN_FAIL.value) {
        return true
      }
      return false
    },
    async handleCodeRenew(taskId, nodeId) {
      await API.renewNodeCode({ taskId, nodeId, redo: 1 })
      this.$message.success('代码更新成功')
      console.log('code renew')
    },
    checkBuild() {
      const type = this.currentNode.type
      if (type === TASK_NODE_LIST.TOOL) {
        return false
      }
      const status = this.taskInfo.status
      return status === TASK_STATES_ENUM.BUILT.value ||
        status === TASK_STATES_ENUM.RE_DOWNLOADED.value ||
        status === TASK_STATES_ENUM.RE_DOWNLOAD_FAIL.value ||
        status === TASK_STATES_ENUM.RAN.value ||
        status === TASK_STATES_ENUM.BUILD_FAIL.value
    },
    async handleBuild(taskId, nodeId) {
      console.log('handleRedeployNode: ', taskId, nodeId)
      await API.reDeployTaskNode({ taskId, nodeId, redo: 1 })
      this.$message.success('节点重搭建启动成功')
    }
  }
}
</script>

<style scoped>
ul {
  list-style-type: none;
  padding: 0;
}
li {
  cursor: pointer;
  padding: 8px;
  border: 1px solid #ccc;
  margin-bottom: 4px;
}
li:hover {
  background-color: #f0f0f0;
}
.c-node-menu-item{
  line-height: 30px;
  padding-left: 10px;
  cursor: pointer;
  color: #444444;
  font-size: 14px;
  border-top: #efefef solid 1px;
}
.c-node-menu-item:hover{
  background-color: rgba(66,187,66,0.2);
}
.c-node-tag {
  line-height: 25px;
  padding-left: 10px;
  color: #888888;
  font-size: 12px;
}
.node-menu-panel {
  z-index: 999;
  padding: 10px;
  background-color: #ffffff;
  border: #eeeeee solid 1px;
  box-shadow: 0px 0px 8px #cccccc;
  position: absolute;
  border-radius: 10px;
}
</style>

<template>
  <el-card>
    <el-descriptions :column="4" border class="custom-descriptions" size="small">
      <el-descriptions-item label="任务名">
        {{ taskInfo.name }}
        <el-button v-if="taskInfo.isEditable" size="mini" type="success" @click="showAuthDialog">授权</el-button>
      </el-descriptions-item>
      <el-descriptions-item label="创建人">  <j-me-chat-button :j-me-chat-erp="taskInfo && taskInfo.username" :j-me-chat-name="taskInfo && taskInfo.fullname" /></el-descriptions-item>
      <el-descriptions-item label="拓扑名">{{ taskInfo.topologyName }}</el-descriptions-item>
      <el-descriptions-item label="任务状态">
        <task-status :status="taskInfo.status" />
      </el-descriptions-item>
      <el-descriptions-item label="机器到期时间">
        {{ taskInfo.expiredTime }}
        <el-button v-if="showDelayButton()" size="mini"
                   :disabled="isButtonDisabled()"
                   type="success" @click="handleDelay"
        >
          延期
        </el-button>
        <el-button v-if="taskInfo.isExpired" size="mini"
                   :disabled="true"
                   type="success"
        >
          已到期
        </el-button>
        <!-- <el-button size="mini" type="success" @click="handleHolidayDelay">假日延期</el-button> -->
      </el-descriptions-item>
      <el-descriptions-item label="拓扑负责人">
        <j-me-chat-button :j-me-chat-erp="taskInfo.topologyCreator && taskInfo.topologyCreator.username" :j-me-chat-name="taskInfo.topologyCreator && taskInfo.topologyCreator.fullname" />
      </el-descriptions-item>
      <el-descriptions-item label="操作">
        <task-operate :status="taskInfo.status" :task-id="taskInfo.id" :editable="taskInfo.isEditable" :viewable="true"
                      :is-list="false" :is-update="true"
        />
      </el-descriptions-item>
      <el-descriptions-item label="测试报告">
        <el-button size="mini" type="success" @click="viewReport">查看</el-button>
      </el-descriptions-item>
    </el-descriptions>
    <task-auth-dialog
      :task-id="taskId"
      :visible="authDialogVisible"
      :authorized-users="authorizedUsers"
      @close="handleAuthClose"
    />
    <test-report-table
      :visible="reportVisible"
      :task-id="taskId"
      @close="handleTestReportTableClose"
    />
  </el-card>
</template>

<script>
import TaskOperate from '@/views/task/components/TaskOperate.vue'
import TaskStatus from '@/views/task/components/TaskStatus.vue'
import JMeChatButton from '@/components/common/JMChat/JMeChatButton.vue'
import TestReportTable from '@/views/task/view/components/TestReportTable.vue'
import TaskAuthDialog from '@/views/task/view/components/TaskAuthDialog.vue'
import { CommonModule } from '@/store/modules/common'
import { UserRole } from '@/enums/userRole'

export default {
  name: 'TaskOperationCard',
  components: {
    TaskAuthDialog,
    TestReportTable,
    JMeChatButton,
    TaskStatus,
    TaskOperate
  },
  props: {
    taskId: {
      type: [Number, String],
      required: true,
      default: 0
    },
    taskInfo: {
      type: Object,
      required: true,
      default: () => {}
    }
  },
  data() {
    return {
      authDialogVisible: false,
      reportVisible: false,
      authorizedUsers: []
    }
  },
  computed: {
    isRoot() {
      return CommonModule.role === UserRole.ROOT_USER.value
    }
  },
  created() {
    this.authorizedUsers = this.taskInfo.authUsersName
    console.log(this.authorizedUsers)
  },
  methods: {
    showDelayButton() {
      console.log(this.taskInfo.isExpiringSoon, this.taskInfo.isEditable, this.taskInfo.notOversell)
      return (
        // 快过期，有编辑权限，超卖类型，显示延期按钮
        (this.taskInfo.isExpiringSoon && this.taskInfo.isEditable && this.taskInfo.notOversell === 0)
      )
    },
    showAuthDialog() {
      this.authDialogVisible = true
    },
    handleAuthClose() {
      this.authDialogVisible = false
    },
    handleDelay() {
      console.log('点击了延期按钮')
    },
    viewReport() {
      this.reportVisible = true
    },
    handleTestReportTableClose() {
      this.reportVisible = false
    },
    // 按钮是否禁用, 过期了禁用
    isButtonDisabled() {
      // 管理员角色时，可以点击延期
      return !this.isRoot && !this.taskInfo.isEditable
    }
  }
}
</script>

<style scoped>
.custom-descriptions .el-descriptions-item__label {
  width: 120px; /* 设置统一的标签宽度 */
}
</style>

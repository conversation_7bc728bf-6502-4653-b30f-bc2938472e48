<template>
  <div>
    <div class="editor-toolbar">
      <el-button v-if="currentTask.isEditable" type="primary" @click="showDiff">Diff</el-button>
      <el-button v-if="!isEditing && currentTask.isEditable" type="primary" @click="startEditing">编辑</el-button>
      <el-button v-if="isEditing && currentTask.isEditable" type="success" @click="saveFile">保存</el-button>
    </div>
    <el-row :gutter="20" class="horizontal-layout">
      <!-- 左侧文件树 -->
      <el-col :span="6" class="file-tree-container">
        <FileTree :task-id="taskNodeInfo.taskId" :node-id="taskNodeInfo.id" @file-click="handleFileClick" />
      </el-col>
      <!-- 右侧文档内容 -->
      <el-col :span="18" class="editor-wrapper">
        <div v-if="fileContent" class="editor-container">
          <monaco-editor
            v-model="fileContent"
            language="text"
            theme="vs"
            :options="editorOptions"
          />
        </div>
        <div v-else class="placeholder">
          请点击左侧文件树选择文件
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import FileTree from './FileTree.vue'
import MonacoEditor from 'monaco-editor-vue'
import API from '@/api'
import { Message } from '@lui/lui-ui'
import { TaskModule } from '@/store/modules/task'
export default {
  name: 'NodeConfig',
  components: {
    FileTree,
    MonacoEditor
  },
  props: {
    taskInfo: {
      type: Object,
      required: false,
      default: () => {}
    }
  },
  data() {
    return {
      fileContent: '', // 文件内容
      isEditing: false, // 是否处于编辑状态
      editorOptions: {
        readOnly: true, // 默认设置为只读
        automaticLayout: true // 自动调整布局
      },
      currentFilePath: '' // 当前文件路径
    }
  },
  computed: {
    taskNodeInfo() {
      return TaskModule.taskNodeInfo || {} // 确保默认值为空数组
    },
    currentTask() {
      return this.taskInfo || {} // 确保默认值为空数组
    }
  },
  methods: {
    // 处理文件点击事件
    handleFileClick(filePath) {
      console.log('父组件接收到文件点击事件:', filePath)
      this.currentFilePath = filePath
      this.fetchFileContent(filePath)
    },
    // 模拟从后台获取文件内容
    async fetchFileContent(filePath) {
      try {
        const response = await API.readFile({ path: filePath })
        this.fileContent = response.data
      } catch (error) {
        console.error('获取文件内容失败:', error)
      }
    },
    // 开始编辑
    startEditing() {
      this.isEditing = true
      this.editorOptions.readOnly = false
    },
    // 保存文件
    saveFile() {
      this.isEditing = false
      this.editorOptions.readOnly = true
      this.saveFileContent(this.currentFilePath, this.fileContent)
    },
    // 模拟保存文件内容到后台
    async saveFileContent(filePath, content) {
      const response = await API.saveFile({ filePath, content })
      if (response.data) {
        Message.success('文件保存成功')
      }
    },
    // 显示Diff
    showDiff() {
      this.fetchDiffContent(this.currentFilePath)
    },
    // 模拟从后台获取Diff内容
    fetchDiffContent(filePath) {
      // 模拟异步请求
      setTimeout(() => {
        const mockDiff = `diff --git a/${filePath} b/${filePath}
index 1234567..89abcde 100644
--- a/${filePath}
+++ b/${filePath}
@@ -1,3 +1,3 @@
-echo 'Hello, World!'
+echo 'Hello, Vue!'
`
        this.$alert(mockDiff, 'Diff内容', {
          confirmButtonText: '确定',
          customClass: 'diff-dialog'
        })
      }, 100)
    }
  }
}
</script>

<style scoped>
.horizontal-layout {
  display: flex;
  height: calc(100vh - 50px); /* 减去工具栏的高度 */
}
.file-tree-container {
  height: 100%;
  border-right: 1px solid #dcdfe6; /* 添加分割线 */
  overflow-y: auto; /* 允许文件树滚动 */
}
.editor-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1; /* 占据剩余空间 */
}
.editor-toolbar {
  margin-bottom: 10px;
  margin-left: calc(100% * 1/4);
}
.editor-container {
  flex: 1;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}
.placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #909399;
  font-size: 16px;
}
.diff-dialog {
  white-space: pre-wrap;
  font-family: monospace;
}
</style>

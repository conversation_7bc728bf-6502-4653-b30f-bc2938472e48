<template>
  <div>
    <div style="width: calc(100% - 2px);height:calc(100vh);">
      <RelationGraph ref="graphRef" :options="userGraphOptions" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineComponent, onMounted, ref } from 'vue';
import RelationGraph from 'relation-graph-vue3';
import type { RGJsonData, RGNode, RGLine, RGUserEvent, RGOptions, RelationGraphComponent } from 'relation-graph-vue3';

const graphRef = ref<RelationGraphComponent | null>(null);
const userGraphOptions: RGOptions = {
    'backgrounImage': '',
    'backgrounImageNoRepeat': true,
    'layout': {
      'label': '手工',
      'layoutName': 'fixed',
      'layoutClassName': 'seeks-layout-fixed',
      'defaultJunctionPoint': 'border',
      'defaultNodeShape': 0,
      'defaultLineShape': 1
    },
    'defaultNodeBorderWidth': 0,
    'defaultNodeShape': 1,
    'defaultJunctionPoint': 'lr',
    'defaultLineShape': 1,
    defaultLineTextOffset_y: -2
};

onMounted(() => {
    setGraphData();
});

const setGraphData = async() => {
    const _orign_data = {
        entname: '小小的大米科技股份有限公司',
        invs: [
            { id: 'inv1', text: '北京某个公司科技有限公司', desc: '40%' },
            { id: 'inv2', text: '张蜈支', desc: '30%' },
            { id: 'inv3', text: '如花', desc: '10%' },
            { id: 'inv4', text: '路人甲', desc: '10%' },
            { id: 'inv5', text: '路人乙', desc: '10%' }
        ],
        persons: [
            { id: 'person1', text: '张蜈支', desc: '董事长' },
            { id: 'person2', text: '包奥曼', desc: '总经理' },
            { id: 'person3', text: '路人甲', desc: '监事' },
            { id: 'person4', text: '路人乙', desc: '董事' }
        ],
        asInvs: [
            { id: 'asinv1', text: '北京超级大橘科技有限公司', desc: '80%' },
            { id: 'asinv2', text: '北京超级大蚂蚁科技有限公司', desc: '70%' },
            { id: 'asinv3', text: '北京超级大米粒儿科技有限公司', desc: '20%' }
        ],
        branchs: [
            { id: 'branch1', text: '某个公司（北京）科技股份有限公司', desc: '80%' },
            { id: 'branch2', text: '某个公司（天津）科技股份有限公司', desc: '70%' },
            { id: 'branch4', text: '某个公司（成都）科技股份有限公司', desc: '70%' },
            { id: 'branch5', text: '某个公司（武汉）科技股份有限公司', desc: '20%' }
        ]
    };

    const _center = {
        x: 0,
        y: 0

    };
    const graphData: RGJsonData = {
        rootId: 'root',
        nodes: [],
        lines: []
    };
    const rootNode: RGNode = { id: graphData.rootId, text: _orign_data.entname, styleClass: 'c-g-center', color: '#A4C1FF', width: 250, height: 50, x: _center.x - 125, y: _center.y - 25 };
    const invRootNode: RGNode = { id: 'invRoot', text: '股东', expandHolderPosition: 'left', styleClass: 'c-g-group-node', color: '#FFC5A6', width: 100, height: 50 };
    const personRootNode: RGNode = { id: 'personRoot', text: '高管', expandHolderPosition: 'left', styleClass: 'c-g-group-node', color: '#B9FFA7', width: 100, height: 50 };
    const asinvRootNode: RGNode = { id: 'asinvRoot', text: '对外投资', expandHolderPosition: 'right', styleClass: 'c-g-group-node', color: '#FFBEC1', width: 100, height: 50 };
    const branchRootNode: RGNode = { id: 'branchRoot', text: '分支机构', expandHolderPosition: 'right', styleClass: 'c-g-group-node', color: '#FFA1F8', width: 100, height: 50 };
    invRootNode.x = _center.x - 200 - invRootNode.width;
    invRootNode.y = _center.y - 130;
    personRootNode.x = _center.x - 200 - personRootNode.width;
    personRootNode.y = _center.y + 90;
    asinvRootNode.x = _center.x + 200;
    asinvRootNode.y = _center.y - 130;
    branchRootNode.x = _center.x + 200;
    branchRootNode.y = _center.y + 90;
    graphData.nodes.push(rootNode);
    graphData.nodes.push(invRootNode);
    graphData.nodes.push(personRootNode);
    graphData.nodes.push(asinvRootNode);
    graphData.nodes.push(branchRootNode);
    graphData.lines.push({ from: rootNode.id, to: invRootNode.id, styleClass: 'c-g-l-group', color: '#C7E9FF', lineShape: 2 });
    graphData.lines.push({ from: rootNode.id, to: personRootNode.id, styleClass: 'c-g-l-group', color: '#C7E9FF', lineShape: 2 });
    graphData.lines.push({ from: rootNode.id, to: asinvRootNode.id, styleClass: 'c-g-l-group', color: '#C7E9FF', lineShape: 2 });
    graphData.lines.push({ from: rootNode.id, to: branchRootNode.id, styleClass: 'c-g-l-group', color: '#C7E9FF', lineShape: 2 });
    _orign_data.invs.forEach((thisNode, _index) => {
        thisNode.width = 200;
        thisNode.x = invRootNode.x - 300 - thisNode.width;
        thisNode.y = invRootNode.y + _index * 30 * -1 + 30;
        graphData.nodes.push(thisNode);
        graphData.lines.push({ from: invRootNode.id, to: thisNode.id, text: thisNode.desc, color: '#FFC5A6', arrow: 'none', lineShape: 4 });
    });
    _orign_data.persons.forEach((thisNode, _index) => {
        thisNode.width = 200;
        thisNode.x = personRootNode.x - 200 - thisNode.width;
        thisNode.y = personRootNode.y + _index * 30;
        graphData.nodes.push(thisNode);
        graphData.lines.push({ from: personRootNode.id, to: thisNode.id, text: thisNode.desc, color: '#B9FFA7', arrow: 'none', lineShape: 4 });
    });
    _orign_data.asInvs.forEach((thisNode, _index) => {
        thisNode.x = asinvRootNode.x + 200;
        thisNode.y = asinvRootNode.y + _index * 30 * -1 + 30;
        thisNode.width = 300;
        thisNode.styleClass = 'my-node-class';
        graphData.nodes.push(thisNode);
        graphData.lines.push({ from: asinvRootNode.id, to: thisNode.id, text: thisNode.desc, color: '#FFBEC1', lineShape: 4 });
    });
    _orign_data.branchs.forEach((thisNode, _index) => {
        thisNode.x = branchRootNode.x + 200;
        thisNode.y = branchRootNode.y + _index * 30;
        thisNode.width = 300;
        thisNode.styleClass = 'my-node-class';
        graphData.nodes.push(thisNode);
        graphData.lines.push({ from: branchRootNode.id, to: thisNode.id, text: thisNode.desc, color: '#FFA1F8', lineShape: 4 });
    });
    const graphInstance = graphRef.value?.getInstance();
    if (graphInstance) {
        await graphInstance.setJsonData(graphData);
        await graphInstance.moveToCenter();
        await graphInstance.zoomToFit();
    }
};
</script>

<style scoped>
::v-deep(.relation-graph) {
    .my-node-class {
        width: 260px;
    }
}
</style>

<template>
  <div class="report-setting-container">
    <!-- 报告定制开关按钮 -->
    <div class="report-setting-switch">
      <span class="switch-label">报告定制：</span>
      <el-switch
        v-model="reportEnabled"
        active-color="#13ce66"
        inactive-color="#ff4949"
        @change="handleSwitchChange"
      />
      <el-button
        type="primary"
        size="small"
        class="view-btn"
        @click="handleViewReport"
      >
        查看报告定制
      </el-button>
      <el-button
        type="info"
        size="small"
        class="view-btn"
        @click="handleResetReportSetting"
      >
        获取默认配置
      </el-button>
    </div>

    <!-- 抽屉组件 -->
    <el-drawer
      title="报告定制"
      :visible.sync="drawerVisible"
      :with-header="false"
      size="60%"
      custom-class="report-setting-drawer"
    >
      <div class="jdl-hamburger" @click="drawerVisible = false">
        <i class="el-icon-arrow-right" />
      </div>
      <div class="drawer-content">
        <el-row type="flex" align="middle" justify="start" class="drawer-tit">
          <h2>报告定制</h2>
        </el-row>

        <!-- 编辑器区域 -->
        <div class="editor-container">
          <monaco-editor
            v-model="reportSettingsContent"
            language="json"
            theme="vs"
            :options="editorOptions"
          />
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button @click="drawerVisible = false">取消</el-button>
          <el-button type="warning" @click="handleReset">恢复默认</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import MonacoEditor from 'monaco-editor-vue'
import API from '@/api'
import { Message } from '@lui/lui-ui'

export default {
  name: 'ReportSetting',
  components: {
    MonacoEditor
  },
  props: {
    topologyId: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      // 报告定制开关状态，由API返回的isOpen值决定
      reportEnabled: false,
      // 报告设置ID，用于API调用
      reportSettingId: 0,
      // 抽屉显示状态
      drawerVisible: false,
      // 报告设置内容
      reportSettingsContent: '',
      // 原始设置内容，用于比较是否有修改
      originalSettings: '',
      editorOptions: {
        automaticLayout: true,
        formatOnPaste: true,
        formatOnType: true,
        scrollBeyondLastLine: false,
        minimap: { enabled: false },
        lineNumbers: 'on',
        roundedSelection: false,
        wordWrap: 'on',
        folding: true,
        renderLineHighlight: 'all',
        fontSize: 14,
        tabSize: 2
      },
      loading: false
    }
  },
  created() {
    // 组件创建时自动获取报告设置信息，并根据isOpen值设置开关状态
    this.fetchReportSettingInfo()
  },
  methods: {
    // 获取报告设置信息
    async fetchReportSettingInfo() {
      try {
        this.loading = true
        const response = await API.getReportSettingInfo({ topologyId: this.topologyId })
        console.log('fetch report setting : ', response)
        if (response.code === 0) {
          // 根据API返回的isOpen值设置开关状态
          this.reportEnabled = response.data.open || false
          // 保存报告设置ID，用于后续API调用
          this.reportSettingId = response.data.id || 0

          // 处理可能包含回车符的JSON内容
          let reportSettings = response.data.reportSettings || {}

          // 如果是字符串，尝试解析
          if (typeof reportSettings === 'string') {
            try {
              // 处理带有转义字符的JSON字符串，如 "{\n  \"header\": {},\n  \"type\": \"report_settings\" \n}"
              // 先尝试直接解析
              reportSettings = JSON.parse(reportSettings)
            } catch (e) {
              console.error('第一次解析报告设置JSON字符串失败，尝试处理转义字符:', e)

              try {
                // 如果直接解析失败，可能是因为字符串中包含转义字符
                // 尝试将字符串中的转义字符转换为实际字符
                const unescapedString = reportSettings
                  .replace(/\\n/g, '\n') // 将 \n 转换为实际的换行符
                  .replace(/\\"/g, '"') // 将 \" 转换为实际的引号
                  .replace(/\\t/g, '\t') // 将 \t 转换为实际的制表符
                  .replace(/\\r/g, '\r') // 将 \r 转换为实际的回车符

                // 检查字符串是否以引号开始和结束，如果是，去掉这些引号
                const processedString = unescapedString.startsWith('"') && unescapedString.endsWith('"')
                  ? unescapedString.slice(1, -1)
                  : unescapedString

                // 再次尝试解析
                reportSettings = JSON.parse(processedString)
              } catch (innerError) {
                console.error('第二次解析报告设置JSON字符串失败:', innerError)
                // 如果两次解析都失败，保持原样，但尝试移除转义字符以便更好地显示
                try {
                  // 尝试将字符串中的转义字符转换为实际字符，但不解析为JSON对象
                  reportSettings = reportSettings
                    .replace(/\\n/g, '\n')
                    .replace(/\\"/g, '"')
                    .replace(/\\t/g, '\t')
                    .replace(/\\r/g, '\r')

                  // 如果字符串以引号开始和结束，去掉这些引号
                  if (reportSettings.startsWith('"') && reportSettings.endsWith('"')) {
                    reportSettings = reportSettings.slice(1, -1)
                  }
                } catch (e) {
                  // 如果所有尝试都失败，保持原样
                  console.error('处理转义字符失败，使用原始字符串:', e)
                }
              }
            }
          }

          // 如果reportSettings已经是对象，直接格式化
          // 如果是字符串但看起来像JSON，尝试再次解析
          if (typeof reportSettings === 'string' &&
              (reportSettings.trim().startsWith('{') || reportSettings.trim().startsWith('['))) {
            try {
              reportSettings = JSON.parse(reportSettings)
            } catch (e) {
              console.error('最终解析JSON字符串失败，将按原样显示:', e)
            }
          }

          // 格式化JSON，保留缩进和换行
          this.reportSettingsContent = typeof reportSettings === 'object'
            ? JSON.stringify(reportSettings, null, 2)
            : reportSettings
          this.originalSettings = this.reportSettingsContent
        } else {
          Message.error(response.message || '获取报告设置失败')
        }
      } catch (error) {
        console.error('获取报告设置失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 处理开关变化
    async handleSwitchChange(value) {
      try {
        const response = await API.onoffReportSetting({
          topologyId: this.topologyId,
          open: value
        })
        if (response.code === 0) {
          this.fetchReportSettingInfo()
          Message.success(value ? '报告定制已启用' : '报告定制已禁用')
        } else {
          // 如果失败，恢复开关状态
          this.reportEnabled = !value
          Message.error(response.message || '操作失败')
        }
      } catch (error) {
        console.error('切换报告定制状态失败:', error)
        this.reportEnabled = !value
        Message.error('切换报告定制状态失败')
      }
    },
    openDrawer() {
      this.drawerVisible = true
    },
    // 处理查看报告按钮点击
    async handleViewReport() {
      // 检查是否存在reportSettings内容
      const response = await API.getReportSettingInfo({ topologyId: this.topologyId })
      console.log('查看报告设置： ', response)
      if (!response.data) {
        Message.info('暂时没有配置报告')
        return
      }
      if (response.data) {
        const id = response.data.id
        if (id == null) {
          Message.info('暂时没有配置报告')
          return
        }
      }
      // 如果存在内容，打开抽屉
      this.openDrawer()
    },
    async handleSave() {
      try {
        const response = await API.updateReportSetting({
          id: this.reportSettingId,
          topologyId: this.topologyId,
          reportSettings: this.reportSettingsContent
        })

        if (response.code === 0) {
          Message.success('保存成功')
          // 保存成功后更新原始设置
          this.originalSettings = this.reportSettingsContent
          this.drawerVisible = false
        } else {
          Message.error(response.message || '保存失败')
        }
      } catch (error) {
        console.error('保存报告设置失败:', error)
        Message.error('保存报告设置失败')
      }
    },
    async handleResetReportSetting() {
      console.log('获取默认设置')
      try {
        const response = await API.resetReportSetting({ topologyId: this.topologyId })
        if (response.code === 0) {
          Message.success('已获取默认设置')
          // 重新获取设置
          await this.fetchReportSettingInfo()
        } else {
          Message.error(response.message || '恢复默认设置失败')
        }
      } catch (error) {
        console.error('恢复默认设置失败:', error)
        Message.error('恢复默认设置失败')
      }
    },
    // 重置设置
    async handleReset() {
      try {
        const response = await API.resetReportSetting({ topologyId: this.topologyId })

        if (response.code === 0) {
          Message.success('已恢复默认设置')
          // 重新获取设置
          await this.fetchReportSettingInfo()
        } else {
          Message.error(response.message || '恢复默认设置失败')
        }
      } catch (error) {
        console.error('恢复默认设置失败:', error)
        Message.error('恢复默认设置失败')
      }
    }
  }

}
</script>

<style lang="scss" scoped>
.report-setting-container {
  margin-bottom: 20px;

  .report-setting-switch {
    display: flex;
    align-items: center;

    .switch-label {
      margin-right: 10px;
      font-weight: 500;
    }

    .view-btn {
      margin-left: 15px;
    }
  }
}

.report-setting-drawer {
  &.el-drawer {
    overflow: visible;

    .jdl-hamburger {
      position: absolute;
      top: 9px;
      left: -12px;
      width: 12px;
      height: 42px;
      line-height: 42px;
      text-align: center;
      font-size: 8px;
      color: #fff;
      border-radius: 12px 0 0 12px;
      cursor: pointer;
      background-color: #409EFF;
    }

    .drawer-content {
      padding: 12px 16px;
      height: 100vh;
      overflow-y: auto;

      .drawer-tit {
        margin: 0 -16px 16px;
        padding: 0 16px 12px;
        font-size: 16px;
        color: #333;
        font-weight: 600;
        border-bottom: 1px solid rgba(238,238,238,1);
      }

      .editor-container {
        height: calc(100vh - 150px);
        border: 1px solid #dcdfe6;
        border-radius: 4px;
      }

      .action-buttons {
        margin-top: 20px;
        text-align: right;
      }
    }
  }
}
</style>

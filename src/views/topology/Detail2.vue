<template>
  <div class="task-container">
    <!-- 基本信息 -->
    <div style="background-color: white; padding: 15px; margin-bottom: 15px;box-shadow: 0 2px 4px 0 rgba(0,0,0,.12), 0 0 6px 0 rgba(0,0,0,.04);">
      <el-form ref="form" label-width="140px" size="small" clearable>
        <el-form-item label="名称">
          <el-input v-model="dataFrom.name" style="width: 600px;" clearable />
        </el-form-item>
        <el-form-item label="用途">
          <el-input v-model="dataFrom.describeInfo" style="width: 600px;" clearable />
        </el-form-item>
        <el-form-item label="文档">
          <el-input v-model="dataFrom.helpUrl" style="width: 600px;" clearable />
        </el-form-item>
        <el-form-item label="属性">
          <el-table
            v-if="dataFrom.attrList.length > 0"
            size="mini"
            :data="dataFrom.attrList"
            border
            style="width: 600px; margin-bottom: 10px;"
          >
            <el-table-column
              prop="name"
              label="名称"
            />
            <el-table-column
              prop="value"
              label="值"
            />
            <el-table-column
              label="操作"
              align="center"
              width="60"
            >
              <template slot-scope="scope">
                <el-button
                  v-if="dataFrom.editPower"
                  size="mini"
                  type="text"
                  @click.native.prevent="deleteRow(scope.$index, dataFrom.attrList)"
                >
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-button v-if="dataFrom.editPower" size="mini" @click="addAttr">添加</el-button>
        </el-form-item>
        <el-form-item label="允许申请不超卖资源">
          <el-switch v-model="dataFrom.notOversell" disabled /> <el-link href="https://xbp.jd.com/4/apply/22603" target="_blank" size="mini" type="primary">申请流程</el-link>
        </el-form-item>
        <el-form-item v-if="currentTopoId" label="负责人">
          <el-input v-model="dataFrom.erp" disabled style="width: 300px; margin-right: 15px;" />
          <el-button @click="handleChangeOwner">转移</el-button>
          <el-button @click="handleAllowUser">授权</el-button>
        </el-form-item>
        <el-form-item v-if="dataFrom.editPower">
          <el-button type="primary" @click="handleSave">保存</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 拓扑属性操作 -->
    <el-dialog
      title="添加拓扑属性"
      :visible.sync="attrVisible"
      width="400px"
    >
      <div>
        名称: <el-input v-model.trim="tempAttr.name" style="margin-bottom: 15px;" clearable />
        值: <el-input v-model.trim="tempAttr.value" clearable />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmAttr">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 权限交接 -->
    <el-dialog
      title="转移负责人"
      :visible.sync="ownerVisible"
      width="400px"
    >
      <div>
        请输入有效的ERP: <el-input v-model.trim="changeOwner" style="margin-bottom: 15px;" clearable />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmChangeOwner">确认交接</el-button>
      </span>
    </el-dialog>

    <!-- 授权用户 -->
    <el-dialog
      title="编辑授权用户"
      :visible.sync="allowVisible"
      width="600px"
    >
      <div style="margin-bottom: 10px;">
        <el-tag
          v-for="tag in dataFrom.powerUserList"
          :key="tag.id"
          closable
          @close="handleCloseAllowUser(tag)"
        >
          {{ tag.username }}
        </el-tag>
      </div>

      <el-input v-model.trim="allowUserErp" clearable />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmAllowOwner">保存授权用户</el-button>
      </span>
    </el-dialog>

    <!-- 节点属性 -->
    <el-dialog
      title="添加节点属性"
      :visible.sync="nodeAttrVisible"
      width="400px"
    >
      <div>
        名称: <el-input v-model.trim="tempNodeAttr.name" style="margin-bottom: 15px;" clearable />
        值: <el-input v-model.trim="tempNodeAttr.value" clearable />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmNodeAttr">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 节点编辑与新增 -->
    <el-drawer
      size="32%"
      :title="nodeTitle"
      :visible.sync="nodeDrawer"
    >
      <div style="padding: 10px">
        <el-divider><i class="el-icon-mobile-phone">节点属性</i></el-divider>
        <el-form label-width="80px" style="margin-bottom: 15px;">
          <el-form-item label="节点名">
            <el-input v-model.trim="nodeForm.name" disabled clearable style="width: 400px" />
          </el-form-item>
          <el-form-item label="模块">
            <el-select v-model="nodeForm.moduleId" filterable placeholder="请选择模块" style="width: 400px;" clearable @change="onChangeModule">
              <el-option
                v-for="item in moduleList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="节点类型">
            <span v-if="nodeForm.type === 2" style="font-size: 13px;">
              <strong>工具节点</strong>
            </span>
            <el-radio-group v-else v-model="nodeForm.type" @change="onChangeNodeType">
              <template>
                <el-radio :label="0">base</el-radio>
                <el-radio :label="1">test</el-radio>
              </template>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="依赖节点">
            <el-select v-model="nodeForm.dependencyNodeIdList" multiple placeholder="请选择依赖节点" style="width: 400px;" clearable>
              <el-option
                v-for="item in dependencyNodeItems"
                :key="item.id"
                :label="item.text"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="节点属性">
            <el-table
              v-if="nodeForm.attrList.length > 0"
              size="mini"
              :data="nodeForm.attrList"
              border
              style="width: 400px; margin-bottom: 10px;"
            >
              <el-table-column
                prop="name"
                label="名称"
              />
              <el-table-column
                prop="value"
                label="值"
              />
              <el-table-column
                label="操作"
                align="center"
                width="60"
              >
                <template slot-scope="scope">
                  <el-button
                    v-if="scope.row.name !== 'group'"
                    size="mini"
                    type="text"
                    @click.native.prevent="deleteRow(scope.$index, nodeForm.attrList)"
                  >
                    移除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-button size="mini" @click="addNodeAttr">添加节点属性</el-button>
          </el-form-item>

          <el-form-item v-if="dataFrom.editPower">
            <el-button size="mini" type="primary" @click="handleSaveNode">保存节点</el-button>
            <el-button v-if="nodeForm.id" size="mini" type="danger" @click="handleDelNode">删除节点</el-button>
          </el-form-item>
        </el-form>

        <!-- 模块属性 -->
        <template v-if="nodeForm.moduleInfo">
          <el-divider><i class="el-icon-mobile-phone">模块属性</i></el-divider>
          <el-descriptions class="margin-top" :column="1" size="mini" border>
            <el-descriptions-item>
              <template slot="label">
                模块名称
              </template>
              {{ nodeForm.moduleInfo.name }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                类型
              </template>
              {{ getModuleTypeLabel(nodeForm.moduleInfo.type) }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                Platform
              </template>
              {{ nodeForm.moduleInfo.platform }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                Limitation
              </template>
              {{ getModuleLimitationLabel(nodeForm.moduleInfo.limitation) }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                CPU
              </template>
              {{ nodeForm.moduleInfo.cpu }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                内存
              </template>
              {{ nodeForm.moduleInfo.mem }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                最小磁盘
              </template>
              {{ nodeForm.moduleInfo.minDisk }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                GIT地址
              </template>
              {{ nodeForm.moduleInfo.git }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                Build脚本
              </template>
              {{ nodeForm.moduleInfo.buildScript }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                Update脚本
              </template>
              {{ nodeForm.moduleInfo.updateScript }}
            </el-descriptions-item>
          </el-descriptions>
        </template>
      </div>
    </el-drawer>

    <template v-if="currentTopoId">
      <!-- 报告配置 -->
      <report-setting :topology-id="currentTopoId" />
      <!-- 拓扑图 -->
      <div style="margin-bottom: 5px;">
        <el-button v-if="dataFrom.editPower" @click="openNode">添加节点</el-button>
      </div>

      <!-- 拓扑图 -->
      <div class="img-container">
        <RelationGraph ref="seeksRelationGraph" :options="graphOptions" :on-node-click="onNodeClick" />
      </div>
    </template>
  </div>
</template>

<script>
import RelationGraph from 'relation-graph'
import API from '@/api'
import { MODULE_TYPES, MODULE_MACHINE_LIMIT_TYPES } from '@/settings/module'
import ReportSetting from '@/views/topology/component/ReportSetting.vue'

export default {
  name: 'TopologyDetail',
  components: { RelationGraph, ReportSetting },
  data() {
    return {
      changeOwner: null, // 交接人
      allowUserErp: null, // 授权用户
      nodeAttrVisible: false, // 编辑属性弹窗
      attrVisible: false, // 编辑属性弹窗
      ownerVisible: false, // 权限交接弹窗
      allowVisible: false, // 授权弹窗
      currentTopoId: null, // 当前页面拓扑ID
      nodeDrawer: false,
      nodeTitle: '新增节点',
      dependencyNodeItems: [], // 当前可选的依赖节点，需要排除自身
      nodeForm: { // 当前节点
        id: null,
        name: null, // 节点名
        moduleId: null,
        dependencyNodeIdList: [],
        attrList: [],
        type: null, // 节点类型
        moduleInfo: null
      },
      dataFrom: { // 当前拓扑详情
        name: null,
        describeInfo: null,
        isAutoPlacement: false,
        helpUrl: null,
        attr: null, // 属性
        notOversell: false,
        erp: null,
        attrList: [],
        nodeLines: [],
        nodes: [],
        powerUserList: [], // 有权限的用户
        editPower: false // 编辑权限
      },
      tempAttr: { // 临时拓扑属性
        name: null,
        value: null
      },
      tempNodeAttr: { // 临时拓扑属性
        name: null,
        value: null
      },
      moduleList: [], // 模块列表
      nodeMenuPanelPosition: { x: 0, y: 0 },
      drawerTitle: '编辑节点',
      graphOptions: {
        defaultNodeBorderWidth: 2,
        defaultNodeColor: 'rgba(238, 178, 94, 0.8)',
        defaultLineWidth: 2,
        placeSingleNode: true,
        disableZoom: false, // 启用数据缩放
        allowSwitchLineShape: true, // 启用线条形状切换
        allowSwitchJunctionPoint: true, // 启用连接点切换
        defaultJunctionPoint: 'border',
        allowAutoLayoutIfSupport: false, // 禁用自动布局
        layouts: [
          {
            type: 'force',
            options: {
              nodeStrength: -300,
              edgeStrength: 0.8,
              nodeDistance: 250
            }
          }
        ],
        defaultNodeWidth: 180,
        defaultNodeHeight: 60,
        defaultLineShape: 2, // 曲线
        useLayoutAnimation: true, // 使用布局动画
        nodePositionCalculator: (node, graphInstance) => {
          const index = graphInstance.nodes.indexOf(node)
          const totalNodes = graphInstance.nodes.length
          const containerWidth = graphInstance.canvasSize.width
          const containerHeight = graphInstance.canvasSize.height
          const nodeWidth = node.width || graphInstance.options.defaultNodeWidth
          const nodeHeight = node.height || graphInstance.options.defaultNodeHeight
          const columns = Math.ceil(Math.sqrt(totalNodes))
          const rows = Math.ceil(totalNodes / columns)
          const horizontalSpacing = containerWidth / (columns + 1)
          const verticalSpacing = containerHeight / (rows + 1)
          const row = Math.floor(index / columns)
          const column = index % columns

          return {
            x: (column + 1) * horizontalSpacing - nodeWidth / 2,
            y: (row + 1) * verticalSpacing - nodeHeight / 2
          }
        }
      }
    }
  },
  created() {
    this.initGraphData()
  },
  methods: {
    handleChangeOwner() {
      this.changeOwner = null
      this.ownerVisible = true
    },
    handleAllowUser() {
      // 授权弹窗
      this.allowUserErp = null
      this.allowVisible = true
    },
    initGraphData() {
      const id = this.$route.params.id
      console.log('当前ID', id)
      if (id && id > 0) {
        this.currentTopoId = id
        this.getDetail()
      } else {
        this.currentTopoId = null
        this.dataFrom.editPower = true
      }
    },
    deleteRow(index, rows) {
      // 删除属性，动态
      rows.splice(index, 1)
    },
    addNodeAttr() {
      // 添加节点属性
      this.tempNodeAttr = { // 清空原来的默认是
        name: null,
        value: null
      }
      this.nodeAttrVisible = true
    },
    addAttr() {
      // 添加属性
      this.tempAttr = { // 清空原来的默认是
        name: null,
        value: null
      }
      this.attrVisible = true
    },
    async confirmChangeOwner() {
      // 交接负责人
      const params = { id: this.currentTopoId, owner: this.changeOwner }
      console.log('交接', params)
      await API.changeTopologyOwner(params)
      this.ownerVisible = false
      await this.getDetail()
      this.$message.success('转移负责人成功！')
    },
    async confirmAllowOwner() {
      // 添加授权人
      const params = { newErp: this.allowUserErp, erpList: this.dataFrom.powerUserList || [], id: this.currentTopoId }
      console.log('授权用户', params)
      await API.saveTopologyAllowUser(params)
      this.allowVisible = false
      this.$message.success('保存成功！')

      await this.getDetail()
    },
    async handleDelNode() {
      // 删除节点
      const params = {
        topologyId: this.currentTopoId,
        nodeId: this.nodeForm.id
      }

      console.log('删除节点', params)
      await API.delNodeById(params)
      this.nodeDrawer = false
      this.$message.success('删除节点成功！')

      await this.getDetail()
    },
    confirmAttr() {
      // 确认添加属性
      const params = {
        name: this.tempAttr.name,
        value: this.tempAttr.value
      }
      if (params.name === '' || params.name === undefined || params.name === null) {
        this.$message.error('名称不合法！')
        return
      }

      if (params.value === '' || params.value === undefined || params.value === null) {
        this.$message.error('值不合法！')
        return
      }
      this.dataFrom.attrList.push(params)
      this.$message.success('添加成功! 保存生效！')
      this.attrVisible = false
    },
    confirmNodeAttr() {
      // 确认添加属性
      const params = {
        name: this.tempNodeAttr.name,
        value: this.tempNodeAttr.value
      }
      if (params.name === '' || params.name === undefined || params.name === null) {
        this.$message.error('名称不合法！')
        return
      }

      if (params.value === '' || params.value === undefined || params.value === null) {
        this.$message.error('值不合法！')
        return
      }
      this.nodeForm.attrList.push(params)
      this.$message.success('添加成功! 保存生效！')
      this.nodeAttrVisible = false
    },
    async getDetail() {
      // 获取详情
      const response = await API.fetchTopolopyDetailById({ id: this.currentTopoId }) // 调用 API 获取任务列表
      console.log('=======', response)

      if (response.code === 0) {
        this.dataFrom = response.data
      }

      setTimeout(() => {
        this.showSeeksGraph()
      }, 500)
    },
    onChangeModule(moduleId) {
      // 编辑节点，切换模块时
      console.log('切换模块', moduleId)
      const module = this.moduleList.find(item => item.id === moduleId) // 找模块基础
      console.log('当前模块: ', module)

      // 从已有的里面去统计已经存在的个数
      const count = this.dataFrom.nodes.filter(item => item.moduleId === moduleId).length // 统计
      console.log('统计', count)
      // 1代表工具节点，0代表应用节点
      if (module.type === 1) {
        // 工具节点
        this.nodeForm.type = 2 // 工具节点固定是2
        this.nodeForm.name = module.name + '_' + count
      } else if (module.type === 0) {
        this.nodeForm.type = 0 // 代表应用节点下的base
        this.nodeForm.name = module.name + '_base_' + count
      }

      this.nodeForm.moduleId = moduleId
      this.nodeForm.moduleInfo = module
      if (this.nodeForm.id) {
        // 编辑
      } else {
        // 新增
        this.nodeForm.attrList = [{ name: 'group', value: count }] // TODO 加上模块自身的属性
      }

      // 新增默认加上group和模块自身属性
      // 编辑则不变

      console.log('this.nodeForm.attrList', this.nodeForm.attrList)
    },
    onChangeNodeType() {
      console.log('切换类型')
      const count = this.dataFrom.nodes.filter(item => item.moduleId === this.nodeForm.moduleId).length // 统计
      console.log('统计', count)
      if (this.nodeForm.type === 0) {
        this.nodeForm.name = this.nodeForm.moduleInfo.name + '_base_' + count
      } else if (this.nodeForm.type === 1) {
        this.nodeForm.name = this.nodeForm.moduleInfo.name + '_test_' + count
      }
    },
    async handleSaveNode() {
      const dependencyNodes = []
      const dependencyNodeIdList = this.nodeForm.dependencyNodeIdList

      // 依赖的节点ID
      if (dependencyNodeIdList instanceof Array && dependencyNodeIdList.length > 0) {
        for (let i = 0; i < dependencyNodeIdList.length; i++) {
          const nodeId = dependencyNodeIdList[i]
          const node = this.dataFrom.nodes.find(item => item.id === nodeId)
          console.log('当前依赖的节点信息', node)
          const moduleId = node.moduleId
          dependencyNodes.push({
            nodeId: nodeId,
            moduleId: moduleId
          })
        }
      }

      // 保存节点
      const params = {
        id: this.nodeForm.id,
        moduleId: this.nodeForm.moduleId,
        type: this.nodeForm.type,
        name: this.nodeForm.name,
        topologyId: this.currentTopoId,
        attrList: this.nodeForm.attrList,
        dependencyNodes: dependencyNodes

      }
      if (!params.moduleId) {
        this.$message.success('所选模块不能为空！')
        return
      }

      console.log('保存节点: ', params)
      await API.saveNode(params)
      this.$message.success('保存成功！')
      this.nodeDrawer = false

      await this.getDetail()
    },

    async handleSave() {
      // 保存拓扑
      const params = {
        id: this.currentTopoId,
        name: this.dataFrom.name,
        describeInfo: this.dataFrom.describeInfo,
        isAutoPlacement: this.dataFrom.isAutoPlacement,
        helpUrl: this.dataFrom.helpUrl,
        attrList: this.dataFrom.attrList, // 属性
        notOversell: this.dataFrom.notOversell
      }
      console.log('保存内容', params)
      if (!params.name) {
        this.$message.error('名称不能为空！')
        return
      }

      const res = await API.saveTopolopy(params)
      console.log('保存结果', res)

      if (!this.currentTopoId) {
        window.location.href = '/topology'
      }

      this.$message.success('保存成功！')
    },
    async openNode() {
      // 添加节点
      this.nodeForm = { // 当前节点
        id: null,
        name: null, // 节点名
        moduleId: null,
        dependencyNodeIds: [],
        attrList: [],
        type: null, // 节点类型
        moduleInfo: null
      }
      await this.getModuleList()

      this.dependencyNodeItems = this.dataFrom.nodes
      this.nodeTitle = '新增节点'
      this.nodeDrawer = true
    },
    async getModuleList() {
      const res = await API.fetchModuleList()
      // console.log('获取模块列表', res)
      this.moduleList = res.data
    },
    handleCloseAllowUser(tag) {
      // 删除授权用户信息
      this.dataFrom.powerUserList.splice(this.dataFrom.powerUserList.findIndex(item => item.id === tag.id), 1)
    },
    async onNodeClick(nodeObject, $event) {
      await this.getModuleList()
      // 点击拓扑节点
      // console.log('onNodeClick2:', nodeObject)
      const currentNodeId = nodeObject.id
      this.nodeForm = { // 当前节点
        id: null,
        name: null, // 节点名
        moduleId: null,
        dependencyNodeIdList: [],
        attrList: [],
        type: null, // 节点类型
        moduleInfo: null
      }
      const node = this.dataFrom.nodes.find(item => item.id === currentNodeId) // 找模块基础
      this.nodeForm = node
      const module = this.moduleList.find(item => item.id === node.moduleId) // 找模块基础
      this.nodeForm.moduleInfo = module || null
      console.log('当前节点详情', this.nodeForm)

      this.hightNodeLine(nodeObject, $event)

      // console.log('当前节点', this.currentNode)
      this.dependencyNodeItems = this.dataFrom.nodes.filter(item => item.id !== currentNodeId)
      this.nodeTitle = '编辑节点'
      this.nodeDrawer = true
    },
    showSeeksGraph() {
      if (!this.currentTopoId) {
        return
      }
      if (!this.dataFrom.nodes || !this.dataFrom.nodeLines) {
        return
      }
      const params = {
        rootId: this.currentTopoId,
        nodes: this.dataFrom.nodes.map(node => ({
          id: node.id,
          text: node.name,
          nodeShape: 0, // 或其他适当的形状
          width: this.graphOptions.defaultNodeWidth,
          height: this.graphOptions.defaultNodeHeight
        })),
        lines: this.dataFrom.nodeLines.map(line => ({
          from: line.fromNodeId,
          to: line.toNodeId
        }))
      }
      const graphJsonData = params
      if (!graphJsonData) {
        console.log('数据非法')
        return
      }
      console.log('Graph data:', graphJsonData)
      if (this.$refs.seeksRelationGraph) {
        this.$refs.seeksRelationGraph.setJsonData(graphJsonData, (seeksRGGraph) => {
          // 在图表完成渲染后，强制更新节点位置
          seeksRGGraph.refresh()
        })
      } else {
        console.error('seeksRelationGraph ref is not available')
      }
    },
    getModuleTypeLabel(type) {
      // 根据模块类型值返回对应的中文标签
      const moduleType = MODULE_TYPES.find(item => item.value === type)
      return moduleType ? moduleType.label : type
    },
    getModuleLimitationLabel(type) {
      // 根据模块类型值返回对应的中文标签
      const limitationType = MODULE_MACHINE_LIMIT_TYPES.find(item => item.value === type)
      return limitationType ? limitationType.label : type
    },
    hightNodeLine(nodeObject, $event) {
      // console.log('onNodeClick:', nodeObject)
      const allLinks = this.$refs.seeksRelationGraph.getLinks()
      allLinks.forEach(link => { // 还原所有样式
        link.relations.forEach(line => {
          if (line.data.orignColor) {
            // line.color = line.data.orignColor;
            line.color = '#dddddd'
          }
          if (line.data.orignFontColor) {
            line.fontColor = line.data.orignColor
          }
          if (line.data.orignLineWidth) {
            line.lineWidth = line.data.orignLineWidth
          }
        })
      })
      // 让与{nodeObject}相关的所有连线高亮
      allLinks.filter(link => (link.fromNode === nodeObject || link.toNode === nodeObject)).forEach(link => {
        link.relations.forEach(line => {
          // console.log('line:', line)
          line.data.orignColor = line.color
          line.data.orignFontColor = line.fontColor || line.color
          line.data.orignLineWidth = line.lineWidth || 1
          line.color = '#ff0000'
          line.fontColor = '#ff0000'
          line.lineWidth = 3
        })
      })
      // 有时候更改一些属性后，并不能马上同步到视图，这需要以下方法让视图强制根据数据同步到最新
      this.$refs.seeksRelationGraph.getInstance().dataUpdated()
    }

  }
}

</script>
<style scoped>
.task-container {
  padding: 20px;
}

.img-container {
  box-shadow: 0 2px 4px 0 rgba(0,0,0,.12), 0 0 6px 0 rgba(0,0,0,.04);
  width: 100%;
  height: 1000px;
}

::v-deep .my-node-style {
  padding: 5px 10px;
  border: 0px solid !important;
  /*margin: 10px;*/
}

::v-deep .rel-easy-view {
  background-color: #020202 !important;
}

::v-deep .el-table--mini .el-table__cell {
  padding: 0px 0 !important;
}

::v-deep .el-drawer__header {
  margin-bottom: 0 !important;
}
</style>

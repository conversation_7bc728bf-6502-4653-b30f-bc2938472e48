<template>
  <div class="navbar">
    <logo class="hidden-xs-only" />
    <!-- <top-menu /> -->
    <div class="right-menu">
      <!-- <search-box /> -->
      <!-- <a href="javascript:;" class="navbar-ico">
        <i class="el-icon-message" />
      </a> -->
      <div class="avatar-container">
        <div class="avatar-wrapper">
          <!--          <img :src="avatar" class="user-avatar">-->
          <span class="user-name">{{ name || '用户名' }}</span>
          <i class="el-icon-arrow-down" />
        </div>
        <div class="avatar-container-dropdown">
          <!-- <div class="dropdown-item" @click="showChangeTheme = true">
            切换主题
          </div> -->
          <!--          <div class="dropdown-item" @click="goUserCenter">个人中心</div>-->
          <div class="dropdown-item" @click="logout">退出登录</div>
        </div>
      </div>
    </div>
    <el-dialog
      title="选择系统主题"
      :visible.sync="showChangeTheme"
      width="430px"
    >
      <div class="themes" @click="changeTheme">
        <div
          :class="{ 'is-active': theme === 'blue' }"
          class="themes-item blue"
          data-theme="blue"
        >
          <img
            src="~@/assets/img/framework/theme-blue.png"
            data-theme="blue"
            alt=""
          >
          <div class="font_family icon-correct-n" data-theme="blue" />
        </div>
        <div
          :class="{ 'is-active': theme === 'red' }"
          class="themes-item red"
          data-theme="red"
        >
          <img
            src="~@/assets/img/framework/theme-red.png"
            data-theme="red"
            alt=""
          >
          <div class="font_family icon-correct-n" data-theme="red" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { UserModule } from '@/store/modules/user'
import { CommonModule } from '@/store/modules/common'
import { SappModule } from '@/store/modules/app'
import WaterMark from 'watermark-dom'
import Logo from './Logo.vue'
import TopMenu from './TopMenu.vue'
import SearchBox from './Search.vue'

// 主题存储的key
const THEME_KEY = 'jdwl-admin-theme'
export default {
  components: {
    Logo
    // TopMenu,
    // SearchBox
  },
  data() {
    return {
      showChangeTheme: false,
      activeIndex: '2'
    }
  },

  computed: {
    name() {
      return CommonModule.fullname
    },
    ename() {
      return CommonModule.username
    },
    theme() {
      return SappModule.themeColor
    },
    avatar: () =>
      require('@/assets/img/framework/avatar.png')
  },
  watch: {
    ename(newVal, oldVal) {
      if (newVal === oldVal) return
      // 添加水印
      this.addWaterMark(newVal)
    }
  },
  created() {
    this.initTheme()
    // 添加水印
    if (this.ename) {
      this.addWaterMark(this.ename)
    }
  },
  methods: {
    addWaterMark(txt) {
      WaterMark.load({
        watermark_fontsize: '12px',
        watermark_alpha: '0.1',
        watermark_angle: 30,
        watermark_txt: txt
      })
    },
    handleSelect(key, keyPath) {
      console.log(key, keyPath)
    },
    toggleSideBar() {},
    goUserCenter() {
      this.$router.push('/').catch((err) => err)
    },
    logout() {
      UserModule.logOut()
    },
    changeTheme(e) {
      const themeColor = e.target.dataset.theme

      // 兼容点击边缘主题问题
      if (!themeColor) {
        return
      }
      localStorage.setItem(THEME_KEY, themeColor)
      this.changeThemeCore(themeColor)
    },
    // 初始化主题：从存储中
    initTheme() {
      const themeColor = localStorage.getItem(THEME_KEY)
      if (themeColor) {
        this.changeThemeCore(themeColor)
      } else {
        this.changeThemeCore(SappModule.themeColor)
      }
    },
    changeThemeCore(themeColor = 'blue') {
      if (themeColor === 'red') {
        document.body.classList.add('jdwl-admin-red')
      } else {
        document.body.classList.remove('jdwl-admin-red')
      }
      SappModule.SET_THRME_COLOR(themeColor)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/mixins.scss';
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  @include background_color('main-color');
  transition: width 0.28s;
  height: $navHeight;
}
.right-menu {
  display: flex;
  align-items: center;
  float: right;
  height: 100%;
  line-height: $navHeight;

  .change-theme {
    margin-right: 10px;
    ul {
      display: flex;
      justify-content: center;
      align-items: center;
      li {
        width: 18px;
        height: 18px;
        background: rgba(44, 101, 250, 1);
        border: 1px solid rgba(255, 255, 255, 1);
        border-radius: 2px;
        margin-right: 10px;
        cursor: pointer;
        &:last-child {
          margin-right: 0;
        }
      }
      .red {
        background: #e1251b;
      }
      .blue {
        background: #2c65fa;
      }
    }
  }
  .navbar-ico {
    height: 100%;
    padding: 0 12px;
    font-size: 16px;
    i {
      color: #fff;
    }
  }
  .avatar-container {
    padding: 0 32px 0 12px;
    .avatar-wrapper {
      cursor: pointer;
      display: flex;
      align-items: center;
      .user-avatar {
        cursor: pointer;
        width: 40px;
        height: 40px;
        border-radius: 100px;
        vertical-align: middle;
      }
      .user-name {
        display: inline-block;
        color: #fff;
        padding: 0 8px;
      }
      .el-icon-arrow-down {
        font-size: 12px;
        color: #fff;
      }
    }
    &:hover {
      .avatar-container-dropdown {
        display: block;
      }
    }
    &-dropdown {
      display: none;
      position: absolute;
      top: 100%;
      right: 32px;
      background: #fff;
      border-radius: 0 0 4px 4px;
      box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.12);
      .dropdown-item {
        min-width: 140px;
        padding: 12px 22px;
        line-height: 100%;
        cursor: pointer;
        &:hover {
          @include background_color('menu-hover-color');
        }
      }
    }
  }
}
.themes {
  display: flex;
  justify-content: space-around;
  &-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px 14px;
    border: 1px solid transparent;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    img {
      width: 160px;
      margin-bottom: 10px;
    }
    .font_family {
      font-size: 22px;
      color: rgba(237, 237, 237, 1);
    }
    &.is-active {
      .font_family {
        color: rgba(54, 190, 147, 1);
      }
    }
    &:hover {
      &.blue {
        border-color: rgba(219, 226, 246, 1);
        background: rgba(60, 110, 240, 0.04);
      }
      &.red {
        border-color: rgba(249, 219, 217, 1);
        background: rgba(225, 37, 27, 0.04);
      }
    }
  }
}
</style>

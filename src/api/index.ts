import bu from './module/bu'
import login from './module/login'
import task from './module/task'
import taskNode from './module/taskNode'
import common from './module/common'
import machine from './module/machine'
import module from './module/module'
import topology from './module/topology'
import user from './module/user'
import report from './module/report'

// 统一注册API
const API = {
  ...bu,
  ...login,
  ...task,
  ...common,
  ...topology,
  ...taskNode,
  ...machine,
  ...module,
  ...user,
  ...report
}

export default API

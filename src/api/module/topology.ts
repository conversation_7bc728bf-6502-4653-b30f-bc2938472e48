// src/api/module/task.ts
import Axios from '@/lib/request'

const TOPOLOGY_API = {
  fetchTopolopyList: (params: object) => Axios.get('/api/v1/topology/items', { params }), // 获取列表
  disableTopolopyById: (params: { id: number }) => Axios.post(`/api/v1/topology/disable/${params.id}`), // 禁用
  enableTopolopyById: (params: { id: number }) => Axios.post(`/api/v1/topology/enable/${params.id}`), // 启用
  delTopolopyById: (params: { id: number }) => Axios.post(`/api/v1/topology/del/${params.id}`), // 删除
  copy: (params: { id: number }) => Axios.post(`/api/v1/topology/copy/${params.id}`), // 复制

  fetchModuleList: () => Axios.get('/api/v1/topology/getModelList'), // 获取模型列表
  // 获取任务拓扑信息
  fetchTaskTopolopyDetailById: (params: { id: number, taskId: number }) => Axios.get(`/api/v1/topology/task/${params.id}/${params.taskId}`),
  fetchTopolopyDetailById: (params: { id: number }) => Axios.get(`/api/v1/topology/detail/${params.id}`), // 详情
  saveTopolopy: (params: object) => Axios.post('/api/v1/topology/save', params), // 保存
  saveAttr: (params: object) => Axios.post('/api/v1/topology/saveAttr', params), // 保存属性
  changeTopologyOwner: (params: object) => Axios.post('/api/v1/topology/saveOwner', params), // 转移负责人
  saveTopologyAllowUser: (params: { id: number }) => Axios.post('/api/v1/topology/saveAllowUser', params), // 授权
  saveNode: (params: object) => Axios.post('/api/v1/topology/saveNode', params), // 保存或修改节点
  delNodeById: (params: object) => Axios.post('/api/v1/topology/delNodeById', params), // 删除节点
  // 更新测试报告配置
  updateReportSetting: (params: object) => Axios.post('/api/v1/topology/reportSetting/update', params),
  resetReportSetting: (params: object) => Axios.get('/api/v1/topology/reportSetting/reset', { params }),
  getReportSettingInfo: (params: object) => Axios.get('/api/v1/topology/reportSetting/info', { params }),
  onoffReportSetting: (params: object) => Axios.get('/api/v1/topology/reportSetting/onoff', { params })
}

export default TOPOLOGY_API

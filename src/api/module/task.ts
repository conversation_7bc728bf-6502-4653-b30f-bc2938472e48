// src/api/module/task.ts
import gw from '@/lib/requestGW'
import Axios from '@/lib/request'

const TASKAPI = {
  fetchTaskList: (params: object) => Axios.post('/api/v1/task/list', params),
  createTask: (params: object) => Axios.post('/api/v1/task/create', params),
  deployTask: (params: object) => Axios.get('/api/v1/task/deploy', { params }),
  reDeployTask: (params: object) => Axios.get('/api/v1/task/reDeploy', { params }),
  reDeployTaskNode: (params: object) => Axios.get('/api/v1/task/node/reDeploy', { params }),
  startTask: (params: object) => Axios.get('/api/v1/task/startTask', { params }),
  restartTask: (params: object) => Axios.get('/api/v1/task/restartTask', { params }),
  stopTask: (params: object) => Axios.get('/api/v1/task/stopTask', { params }),
  copyTask: (params: object) => Axios.get('/api/v1/task/copy', { params }),
  stopWait: (params: object) => Axios.get('/api/v1/task/stopWait', { params }),
  changeTaskName: (params: object) => Axios.get('/api/v1/task/change/name', { params }),
  getFileList: (params: object) => Axios.get('/api/v1/task/file/list', { params }),
  saveFile: (params: object) => Axios.post('/api/v1/task/file/save', params),
  readFile: (params: object) => Axios.get('/api/v1/task/file/read', { params }),
  taskAuthUser: (params: object) => Axios.get('/api/v1/task/auth/user', { params }),
  fetchTaskInfo: (params: object) => Axios.get('/api/v1/task/info', { params }),
  fetchTaskNodeList: (params: object) => Axios.get('/api/v1/task/node/list', { params }),
  fetchTaskNodeInfo: (params: object) => Axios.get('/api/v1/task/node/info', { params }),
  getProcessInfo: (taskId: number | string, nodeId: number | string) => Axios.get(`/api/v1/task/processInfo/${taskId}/${nodeId}`),
  queryMdcRange: (params: object) => Axios.post('/api/v1/task/mdc-range-query', params),
  recreateTask: (params: Array<object>) => Axios.get('/api/v1/task/recreate', { params }),
  deleteTask: (params: Array<object>) => Axios.get('/api/v1/task/delete', { params }),
  freeTask: (params: Array<object>) => Axios.get('/api/v1/task/free', { params }),
  renewCode: (params: Array<object>) => Axios.get('/api/v1/task/renewCode', { params }),
  renewNodeCode: (params: Array<object>) => Axios.get('/api/v1/task/node/renewCode', { params }),
  updateWorkspace: (params: Array<object>) => Axios.get('/api/v1/task/updateWorkspace', { params }),
  updateNodeWorkspace: (params: Array<object>) => Axios.get('/api/v1/task/node/updateWorkspace', { params }),
  replaceNode: (params: object) => Axios.post('/api/v1/task/node/replace', params),
  checkTaskAllocateStatus: (taskId: Number) => Axios.get(`/api/v1/task/check-allocate-status/${taskId}`)
}

export default TASKAPI
